package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.ImageGroupExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ImageGroupDAO {
    long countByExample(ImageGroupExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ImageGroupDO record);

    int insertSelective(ImageGroupDO record);

    List<ImageGroupDO> selectByExample(ImageGroupExample example);

    ImageGroupDO selectByPrimaryKey(Integer id);

    List<String> selectAllTags();

    ImageGroupDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") ImageGroupDO record, @Param("example") ImageGroupExample example);

    int updateByExample(@Param("record") ImageGroupDO record, @Param("example") ImageGroupExample example);

    int updateByPrimaryKeySelective(ImageGroupDO record);

    int updateByPrimaryKey(ImageGroupDO record);

    int logicalDeleteByExample(@Param("example") ImageGroupExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}