package ai.conrain.aigc.platform.dal.example;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

/**
 * ImageQuery
 *
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
@Data
public class ImageRecordExample implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 图像ID，自增主键 */
    private Integer id;

    private Integer cursor;

    /** 用户 ID */
    private Integer userId;

    /** 图像类型，用于区分服装图、风格示意图 */
    private String type;

    /** 图像元数据，JSON格式 */
    private String metadata;

    /** 是否编辑 */
    private Boolean edited;

    private Boolean paired;

    /** 打标结果 */
    private String result;

    /** 标签 */
    private List<String> tags;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    private String direction;

    private int getLimit() {
        return pageSize != null ? pageSize : 0;
    }

    private int getOffset() {
        return pageNum != null ? (pageNum - 1) * getLimit() : 0;
    }

    private String getTagsStr() {
        return CollectionUtils.isEmpty(tags) ? null : String.join(",", tags);
    }
}
