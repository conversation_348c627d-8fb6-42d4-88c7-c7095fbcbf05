package ${controllerUrl};

import java.util.*;

import ${voPackageUrl}.${entityName}VO;
import ${queryPackageUrl}.${entityName}Query;
import ${serviceUrl}.${entityName}Service;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ${entityName}控制器
 *
 * <AUTHOR>
 * @version ${entityName}Service.java v 0.1 ${createTime}
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/${objectName}")
public class ${entityName}Controller {

	/** ${objectName}Service */
	@Autowired
	private ${entityName}Service ${objectName}Service;
	
	@GetMapping("/getById/{id}")
	public Result<${entityName}VO> getById(@NotNull @PathVariable("id")${idType} id) {
		return Result.success(${objectName}Service.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<${idType}> create(@Valid @RequestBody ${entityName}VO ${objectName}){
		return Result.success(${objectName}Service.insert(${objectName}).getId());
	}

<#if enableDeleteByPrimaryKey>
	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg ${idType} id) {
		${objectName}Service.deleteById(id);
		return Result.success();
	}
</#if>

<#if targetRuntime == "MyBatis3Simple">
	<#if enableUpdateByPrimaryKey>
	@PostMapping("/updateById")
	public Result<?> updateById(@Valid @RequestBody ${entityName}VO ${objectName}){
		${objectName}Service.updateById(${objectName});
		return Result.success();
	}
	</#if>

	@PostMapping("/findAll")
	public Result<List<${entityName}VO>> findAll(){
		return Result.success(${objectName}Service.findAll());
	}

<#else>
<#if enableUpdateByPrimaryKey>
	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody ${entityName}VO ${objectName}){
		${objectName}Service.updateByIdSelective(${objectName});
		return Result.success();
	}
</#if>

	@PostMapping("/queryList")
	public Result<List<${entityName}VO>> query${entityName}List(@Valid @RequestBody ${entityName}Query query){
		return Result.success(${objectName}Service.query${entityName}List(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<${entityName}VO>> get${entityName}ByPage(@Valid @RequestBody ${entityName}Query query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(${objectName}Service.query${entityName}ByPage(query));
	}
</#if>
}