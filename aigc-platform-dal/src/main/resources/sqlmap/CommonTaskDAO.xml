<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.CommonTaskDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.CommonTaskDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="out_task_platform" jdbcType="VARCHAR" property="outTaskPlatform" />
    <result column="out_task_id" jdbcType="VARCHAR" property="outTaskId" />
    <result column="out_task_status" jdbcType="VARCHAR" property="outTaskStatus" />
    <result column="req_biz_params" jdbcType="VARCHAR" property="reqBizParams" />
    <result column="related_biz_type" jdbcType="VARCHAR" property="relatedBizType" />
    <result column="related_biz_id" jdbcType="VARCHAR" property="relatedBizId" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime" />
    <result column="task_end_time" jdbcType="TIMESTAMP" property="taskEndTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="ai.conrain.aigc.platform.dal.entity.CommonTaskDO">
    <result column="complete_request" jdbcType="LONGVARCHAR" property="completeRequest" />
    <result column="ret_detail" jdbcType="LONGVARCHAR" property="retDetail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, operator_id, task_type, task_status, out_task_platform, out_task_id, 
    out_task_status, req_biz_params, related_biz_type, related_biz_id, ext_info, task_start_time,
    task_end_time, create_time, modify_time, deleted
  </sql>
  <sql id="Blob_Column_List">
    complete_request, ret_detail
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.CommonTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from common_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.CommonTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from common_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from common_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from common_task
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from common_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.CommonTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into common_task (user_id, operator_id, task_type, 
      task_status, out_task_platform, out_task_id, 
      out_task_status, req_biz_params, related_biz_type, 
      related_biz_id, ext_info, task_start_time,
      task_end_time, create_time, modify_time, 
      deleted, complete_request, ret_detail
      )
    values (#{userId,jdbcType=INTEGER}, #{operatorId,jdbcType=INTEGER}, #{taskType,jdbcType=VARCHAR}, 
      #{taskStatus,jdbcType=VARCHAR}, #{outTaskPlatform,jdbcType=VARCHAR}, #{outTaskId,jdbcType=VARCHAR}, 
      #{outTaskStatus,jdbcType=VARCHAR}, #{reqBizParams,jdbcType=VARCHAR}, #{relatedBizType,jdbcType=VARCHAR}, 
      #{relatedBizId,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR}, #{taskStartTime,jdbcType=TIMESTAMP},
      #{taskEndTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT}, #{completeRequest,jdbcType=LONGVARCHAR}, #{retDetail,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.CommonTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into common_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="outTaskPlatform != null">
        out_task_platform,
      </if>
      <if test="outTaskId != null">
        out_task_id,
      </if>
      <if test="outTaskStatus != null">
        out_task_status,
      </if>
      <if test="reqBizParams != null">
        req_biz_params,
      </if>
      <if test="relatedBizType != null">
        related_biz_type,
      </if>
      <if test="relatedBizId != null">
        related_biz_id,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="taskStartTime != null">
        task_start_time,
      </if>
      <if test="taskEndTime != null">
        task_end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="completeRequest != null">
        complete_request,
      </if>
      <if test="retDetail != null">
        ret_detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="outTaskPlatform != null">
        #{outTaskPlatform,jdbcType=VARCHAR},
      </if>
      <if test="outTaskId != null">
        #{outTaskId,jdbcType=VARCHAR},
      </if>
      <if test="outTaskStatus != null">
        #{outTaskStatus,jdbcType=VARCHAR},
      </if>
      <if test="reqBizParams != null">
        #{reqBizParams,jdbcType=VARCHAR},
      </if>
      <if test="relatedBizType != null">
        #{relatedBizType,jdbcType=VARCHAR},
      </if>
      <if test="relatedBizId != null">
        #{relatedBizId,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="taskStartTime != null">
        #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskEndTime != null">
        #{taskEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="completeRequest != null">
        #{completeRequest,jdbcType=LONGVARCHAR},
      </if>
      <if test="retDetail != null">
        #{retDetail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.CommonTaskExample" resultType="java.lang.Long">
    select count(*) from common_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="lockById" resultType="ai.conrain.aigc.platform.dal.entity.CommonTaskDO">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from common_task
    where id = #{id,jdbcType=INTEGER}
    for update nowait
  </select>

    <update id="updateByExampleSelective" parameterType="map">
    update common_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.outTaskPlatform != null">
        out_task_platform = #{record.outTaskPlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.outTaskId != null">
        out_task_id = #{record.outTaskId,jdbcType=VARCHAR},
      </if>
      <if test="record.outTaskStatus != null">
        out_task_status = #{record.outTaskStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.reqBizParams != null">
        req_biz_params = #{record.reqBizParams,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedBizType != null">
        related_biz_type = #{record.relatedBizType,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedBizId != null">
        related_biz_id = #{record.relatedBizId,jdbcType=VARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStartTime != null">
        task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskEndTime != null">
        task_end_time = #{record.taskEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.completeRequest != null">
        complete_request = #{record.completeRequest,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.retDetail != null">
        ret_detail = #{record.retDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update common_task
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      task_status = #{record.taskStatus,jdbcType=VARCHAR},
      out_task_platform = #{record.outTaskPlatform,jdbcType=VARCHAR},
      out_task_id = #{record.outTaskId,jdbcType=VARCHAR},
      out_task_status = #{record.outTaskStatus,jdbcType=VARCHAR},
      req_biz_params = #{record.reqBizParams,jdbcType=VARCHAR},
      related_biz_type = #{record.relatedBizType,jdbcType=VARCHAR},
      related_biz_id = #{record.relatedBizId,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      task_end_time = #{record.taskEndTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      complete_request = #{record.completeRequest,jdbcType=LONGVARCHAR},
      ret_detail = #{record.retDetail,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update common_task
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      task_status = #{record.taskStatus,jdbcType=VARCHAR},
      out_task_platform = #{record.outTaskPlatform,jdbcType=VARCHAR},
      out_task_id = #{record.outTaskId,jdbcType=VARCHAR},
      out_task_status = #{record.outTaskStatus,jdbcType=VARCHAR},
      req_biz_params = #{record.reqBizParams,jdbcType=VARCHAR},
      related_biz_type = #{record.relatedBizType,jdbcType=VARCHAR},
      related_biz_id = #{record.relatedBizId,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      task_end_time = #{record.taskEndTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.CommonTaskDO">
    update common_task
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="outTaskPlatform != null">
        out_task_platform = #{outTaskPlatform,jdbcType=VARCHAR},
      </if>
      <if test="outTaskId != null">
        out_task_id = #{outTaskId,jdbcType=VARCHAR},
      </if>
      <if test="outTaskStatus != null">
        out_task_status = #{outTaskStatus,jdbcType=VARCHAR},
      </if>
      <if test="reqBizParams != null">
        req_biz_params = #{reqBizParams,jdbcType=VARCHAR},
      </if>
      <if test="relatedBizType != null">
        related_biz_type = #{relatedBizType,jdbcType=VARCHAR},
      </if>
      <if test="relatedBizId != null">
        related_biz_id = #{relatedBizId,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="taskStartTime != null">
        task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskEndTime != null">
        task_end_time = #{taskEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="completeRequest != null">
        complete_request = #{completeRequest,jdbcType=LONGVARCHAR},
      </if>
      <if test="retDetail != null">
        ret_detail = #{retDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.CommonTaskDO">
    update common_task
    set user_id = #{userId,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      task_type = #{taskType,jdbcType=VARCHAR},
      task_status = #{taskStatus,jdbcType=VARCHAR},
      out_task_platform = #{outTaskPlatform,jdbcType=VARCHAR},
      out_task_id = #{outTaskId,jdbcType=VARCHAR},
      out_task_status = #{outTaskStatus,jdbcType=VARCHAR},
      req_biz_params = #{reqBizParams,jdbcType=VARCHAR},
      related_biz_type = #{relatedBizType,jdbcType=VARCHAR},
      related_biz_id = #{relatedBizId,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      task_end_time = #{taskEndTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      complete_request = #{completeRequest,jdbcType=LONGVARCHAR},
      ret_detail = #{retDetail,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.CommonTaskDO">
    update common_task
    set user_id = #{userId,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      task_type = #{taskType,jdbcType=VARCHAR},
      task_status = #{taskStatus,jdbcType=VARCHAR},
      out_task_platform = #{outTaskPlatform,jdbcType=VARCHAR},
      out_task_id = #{outTaskId,jdbcType=VARCHAR},
      out_task_status = #{outTaskStatus,jdbcType=VARCHAR},
      req_biz_params = #{reqBizParams,jdbcType=VARCHAR},
      related_biz_type = #{relatedBizType,jdbcType=VARCHAR},
      related_biz_id = #{relatedBizId,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      task_end_time = #{taskEndTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update common_task set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update common_task set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>