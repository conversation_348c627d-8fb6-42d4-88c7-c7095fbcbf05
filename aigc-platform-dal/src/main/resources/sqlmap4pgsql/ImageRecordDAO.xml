<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.ImageRecordDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="pair_url" jdbcType="VARCHAR" property="pairUrl" />
    <result column="show_img_url" jdbcType="VARCHAR" property="showImgUrl" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
    <result column="image_hash" jdbcType="VARCHAR" property="imageHash" />
    <result column="metadata" jdbcType="OTHER" property="metadata" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <resultMap id="ImageWithEditedStatusResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageRecordDO" extends="BaseResultMap">
    <result column="edited" property="edited" jdbcType="BOOLEAN"/>
    <result column="editTime" property="editTime" jdbcType="TIMESTAMP"/>
    <result column="result" property="result" jdbcType="OTHER"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, type, url, show_img_url, image_path, image_hash, metadata, create_time, modify_time,
    deleted
  </sql>

  <!-- 公共的图片查询条件 -->
  <sql id="_pageable_conditions">
    <if test="query.id != null">
      AND i.id = #{query.id,jdbcType=BIGINT}
    </if>
    <if test="query.type != null and query.type != ''">
      AND i.type = #{query.type,jdbcType=VARCHAR}
    </if>
    <if test="query.tags != null and query.tags.size() > 0">
      AND jsonb_exists_any(i.metadata -> 'tags', string_to_array(#{query.tagsStr,jdbcType=VARCHAR}, ','))
    </if>
  </sql>

  <!-- 公共的游标分页条件 -->
  <sql id="_cursor_conditions">
    <if test="query.cursor != null and query.direction == 'next'">
      AND i.id > #{query.cursor,jdbcType=BIGINT}
    </if>
    <if test="query.cursor != null and query.direction == 'prev'">
      AND i.id &lt; #{query.cursor,jdbcType=BIGINT}
    </if>
  </sql>

  <!-- 公共的图片用户编辑状态 LEFT JOIN -->
  <sql id="_image_caption_user_join">
    LEFT JOIN (
      SELECT DISTINCT ON (image_id) image_id, modify_time
      FROM image_caption_user
      WHERE deleted = false
        <if test="query.userId != null">
            AND user_id = #{query.userId,jdbcType=BIGINT}
        </if>
      ORDER BY image_id, modify_time DESC
    ) AS icu ON i.id = icu.image_id
  </sql>

  <!-- 公共的图片组用户编辑状态 LEFT JOIN -->
  <sql id="_image_group_caption_user_join">
    LEFT JOIN (
      SELECT DISTINCT ON (image_group_id) image_group_id, modify_time
      FROM image_group_caption_user
      WHERE deleted = false
        <if test="query.userId != null">
            AND user_id = #{query.userId,jdbcType=BIGINT}
        </if>
      ORDER BY image_group_id, modify_time DESC
    ) AS igcu ON ig.id = igcu.image_group_id
  </sql>

  <!-- 公共的图片组标题关联 LEFT JOIN，用于筛选 result 字段是否存在 -->
  <sql id="_image_group_caption_join">
    LEFT JOIN image_group_caption igc ON ig.id = igc.image_group_id AND igc.deleted = false
  </sql>

  <!-- 公共的编辑状态筛选条件 -->
  <sql id="_edited_filter">
    <if test="query.edited != null">
        AND sub.edited = #{query.edited,jdbcType=BOOLEAN}
    </if>
  </sql>

  <!-- 公共的排序条件 -->
  <sql id="_order_by_direction">
    <choose>
      <when test="query.direction == 'prev'">
          ORDER BY sub.id DESC
      </when>
      <otherwise>
          ORDER BY sub.id ASC
      </otherwise>
    </choose>
  </sql>

  <select id="findPageable" resultMap="ImageWithEditedStatusResultMap">
    SELECT * FROM (
      SELECT
        i.id, i.type, i.url, i.show_img_url, i.image_path, i.image_hash, i.metadata, i.create_time, i.modify_time, i.deleted,
        CASE WHEN icu.image_id IS NOT NULL THEN TRUE ELSE FALSE END AS edited,
        icu.modify_time as editTime
      FROM
        image i
      <include refid="_image_caption_user_join" />
      <where>
        i.deleted = false
        <include refid="_pageable_conditions" />
        <include refid="_cursor_conditions" />
      </where>
    ) AS sub
    <where>
        <include refid="_edited_filter" />
    </where>
    <include refid="_order_by_direction" />
    LIMIT #{query.limit} OFFSET #{query.offset}
  </select>

  <select id="findList" resultMap="ImageWithEditedStatusResultMap">
    SELECT * FROM (
      SELECT
        i.id, i.type, i.url, i.show_img_url, i.image_path, i.image_hash, i.metadata, i.create_time, i.modify_time, i.deleted,
        CASE WHEN icu.image_id IS NOT NULL THEN TRUE ELSE FALSE END AS edited,
        icu.modify_time as editTime
      FROM
        image i
      <include refid="_image_caption_user_join" />
      <where>
        i.deleted = false
        <include refid="_pageable_conditions" />
        <include refid="_cursor_conditions" />
      </where>
    ) AS sub
    <where>
        <include refid="_edited_filter" />
    </where>
    <include refid="_order_by_direction" />
    LIMIT #{query.limit}
  </select>

  <select id="countPageable" resultType="long">
    SELECT count(*) FROM (
      SELECT
        i.id,
        CASE WHEN icu.image_id IS NOT NULL THEN TRUE ELSE FALSE END AS edited
      FROM
      image i
      LEFT JOIN (
        SELECT DISTINCT ON (image_id) image_id
        FROM image_caption_user
        WHERE deleted = false
          <if test="query.userId != null">
              AND user_id = #{query.userId,jdbcType=BIGINT}
          </if>
      ) AS icu ON i.id = icu.image_id
      <where>
        i.deleted = false
        <include refid="_pageable_conditions" />
      </where>
    ) AS sub
    <where>
        <include refid="_edited_filter" />
    </where>
  </select>

  <!-- 公共的图片组查询条件 -->
  <sql id="_pageable_conditions_group">
    <if test="query.id != null">
      AND ig.id = #{query.id,jdbcType=BIGINT}
    </if>
    <if test="query.paired != null and query.paired == true">
      AND i2.url is not null
    </if>
    <if test="query.paired != null and query.paired == false">
      AND i2.url is null
    </if>
    <if test="query.tags != null and query.tags.size() > 0">
      AND jsonb_exists_any(ig.metadata -> 'tags', string_to_array(#{query.tagsStr,jdbcType=VARCHAR}, ','))
    </if>
    <if test="query.cursor != null and query.direction == 'next'">
      AND ig.id > #{query.cursor,jdbcType=BIGINT}
    </if>
    <if test="query.cursor != null and query.direction == 'prev'">
      AND ig.id &lt; #{query.cursor,jdbcType=BIGINT}
    </if>
    <!-- 筛选 image_group_caption 表的 result 字段中是否包含指定的 result 值 -->
    <if test="query.result != null and query.result != ''">
      AND jsonb_exists_any(igc.result, string_to_array(#{query.result,jdbcType=VARCHAR}, ','))
    </if>
  </sql>

  <select id="findImageGroupPageable" resultMap="ImageWithEditedStatusResultMap">
    SELECT * FROM (
      SELECT
        ig.id,
        'style' as type,
        i1.url,
        i2.url as pair_url,
        ig.metadata,
        ig.create_time,
        ig.modify_time,
        CASE WHEN igcu.image_group_id IS NOT NULL THEN TRUE ELSE FALSE END AS edited,
        igcu.modify_time as editTime,
        igc.result
      FROM
        image_group ig
      LEFT JOIN image i1 ON (ig.image_ids ->> 0)::bigint = i1.id AND i1.deleted = false
      LEFT JOIN image i2 ON (ig.image_ids ->> 1)::bigint = i2.id AND i2.deleted = false
      <include refid="_image_group_caption_user_join" />
      <include refid="_image_group_caption_join" />
      <where>
        ig.deleted = false
        <include refid="_pageable_conditions_group" />
      </where>
    ) AS sub
    <where>
        <include refid="_edited_filter" />
    </where>
    <include refid="_order_by_direction" />
    LIMIT #{query.limit} OFFSET #{query.offset}
  </select>

  <select id="countImageGroupPageable" resultType="long">
    SELECT count(*) FROM (
      SELECT
        ig.id,
        CASE WHEN igcu.image_group_id IS NOT NULL THEN TRUE ELSE FALSE END AS edited
      FROM
      image_group ig
      LEFT JOIN image i1 ON (ig.image_ids ->> 0)::bigint = i1.id AND i1.deleted = false
      LEFT JOIN image i2 ON (ig.image_ids ->> 1)::bigint = i2.id AND i2.deleted = false
      LEFT JOIN (
        SELECT DISTINCT ON (image_group_id) image_group_id
        FROM image_group_caption_user
        WHERE deleted = false
          <if test="query.userId != null">
              AND user_id = #{query.userId,jdbcType=BIGINT}
          </if>
      ) AS igcu ON ig.id = igcu.image_group_id
      <include refid="_image_group_caption_join" />
      <where>
        ig.deleted = false
        <include refid="_pageable_conditions_group" />
      </where>
    ) AS sub
    <where>
        <include refid="_edited_filter" />
    </where>
  </select>
</mapper>
