package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.helper.FileHelper;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.BatchDownloadRequest;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.FileUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONArray;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
@Validated
@RestController
@RequestMapping("/file")
public class FileController {
    @Value("${comfyui.input.path}")
    private String inputPath;
    @Autowired
    private OssService ossService;
    @Autowired
    private FileDispatch fileDispatch;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private OssHelper ossHelper;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private FileHelper fileHelper;

    private static final String[] MIME_TYPE_WHITELIST = {"image/", "video/", "audio/", "application/pdf"};

    @PostMapping("/upload")
    public Result<String> upload(MultipartFile file) {
        return upload(file, false);
    }

    @PostMapping("/uploadPro")
    public Result<String> uploadOSSAndComfyUI(MultipartFile file) {
        return upload(file, true);
    }

    @PostMapping("/batchDownload")
    public Result<String> batchDownload(@Validated @RequestBody BatchDownloadRequest req) {
        if (CollectionUtils.isEmpty(req.getFileUrls())) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "文件列表为空");
        }
        String zipUrl = ossHelper.createZipFromUrlsAndUpload(req.getFileUrls(), req.getFileName(), true);
        if (StringUtils.isBlank(zipUrl)) {
            return Result.error("文件打包失败");
        }
        return Result.success(zipUrl);
    }

    @PostMapping("/uploadOss2Input")
    public Result<?> uploadOss2Input(@JsonArg @NotBlank String ossUrl) throws IOException {
        doUploadOss2Input(ossUrl);
        return Result.success();
    }

    @PostMapping("/batchUploadOss2Input")
    public Result<?> batchUploadOss2Input(@JsonArg @NotEmpty JSONArray ossUrls) throws IOException {
        for (Object ossUrl : ossUrls) {
            doUploadOss2Input((String)ossUrl);
        }
        return Result.success();
    }

    @PostMapping("/syncFile")
    public Result<?> syncFile(@JsonArg @NotEmpty String path, @JsonArg String fromUrl) throws IOException {
        boolean success = fileHelper.syncFile(path, fromUrl);
        if (success) {
            return Result.success();
        } else {
            return Result.error("同步文件失败");
        }
    }

    /**
     * 执行上传oss文件到comfyui
     *
     * @param ossUrl oss地址
     * @throws IOException 异常
     */
    private void doUploadOss2Input(String ossUrl) throws IOException {
        Integer userId = OperationContextHolder.getMasterUserId();
        String fileName = CommonUtil.getFileNameWithoutExtension(ossUrl);
        String filePath = CommonUtil.getFilePathAndNameFromURL(ossUrl);
        String fileServerUrl = serverHelper.getFileServerUrlByUser(userId);
        String fullPath = inputPath + filePath;
        if (comfyUIService.checkFileExists(fullPath, fileServerUrl)) {
            log.info("文件已存在，跳过上传, path={}", fullPath);
            return;
        }

        //下载oss文件
        String path = ossService.downloadFile(filePath, "/tmp/", fileName);

        //上传文件到comfyui
        fileDispatch.uploadFile(fullPath, Files.newInputStream(Paths.get(path)), userId);
        log.info("文件上传成功,path={}", fullPath);
    }

    /**
     * 执行上传任务
     *
     * @param file              文件
     * @param needUploadComfyUI 是否上传到comfyUI
     * @return 结果
     */
    @NotNull
    private Result<String> upload(MultipartFile file, boolean needUploadComfyUI) {

        try {
            if (file == null) {
                log.error("file 为空");
                return Result.failedWithMessage(ResultCode.PARAM_INVALID, "文件体为空");
            }

            if (!checkContentType(file.getContentType())) {
                log.warn("不支持的文件类型{}", file.getContentType());
                return Result.failedWithMessage(ResultCode.PARAM_INVALID, "不支持的文件类型");
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
            // 先通过 ContentType 获取文件扩展名
            String ext = FileUtils.getExtensionFromContentType(file.getContentType());
            if (StringUtils.isBlank(ext)) {
                ext = FilenameUtils.getExtension(file.getOriginalFilename());// 判断 ext 是否包含问号
                if (ext != null && ext.contains("?")) {
                    // 如果包含问号，则截取问号之前的部分
                    ext = ext.substring(0, ext.indexOf("?"));
                }
                ext = "." + ext;
            }
            //图片素材用于抠图需要强制小写后缀名
            ext = StringUtils.lowerCase(ext);

            String fileName = sdf.format(new Date()) + OperationContextHolder.getMasterUserId() + "/" + (
                needUploadComfyUI ? "prod_fix_" : "") + CommonUtil.uuid() + ext;

            String url = ossService.upload(fileName, file.getInputStream());
            AssertUtil.assertNotBlank(url, "文件上传失败，返回的url为空");

            if (needUploadComfyUI) {
                //fileName = StringUtils.substringAfterLast(fileName, "/");
                fileDispatch.uploadFile(fileName, file.getInputStream(), OperationContextHolder.getMasterUserId());
            }

            return Result.success(url);

        } catch (Throwable t) {
            log.error("upload file failed", t);
            return Result.failedWithMessage(ResultCode.SYS_ERROR, null);
        }
    }

    private boolean checkContentType(String type) {
        if (StringUtils.isNotBlank(type)) {
            for (String mimeType : MIME_TYPE_WHITELIST) {
                if (type.startsWith(mimeType)) {
                    return true;
                }
            }
        }
        return false;
    }
}
