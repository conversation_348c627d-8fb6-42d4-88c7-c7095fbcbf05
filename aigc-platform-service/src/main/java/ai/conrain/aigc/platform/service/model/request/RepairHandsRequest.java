/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 手部修复请求
 *
 * <AUTHOR>
 * @version : RepairHandsRequest.java, v 0.1 2024/7/18 15:26 renxiao.wu Exp $
 */
@Data
public class RepairHandsRequest implements CreativeRequest {
    private static final long serialVersionUID = 3359429821781449644L;

    /** 任务id */
    private Integer taskId;

    /** 服装id */
    private Integer modelId;

    /** 原始图片url */
    @NotBlank
    private String originImage;

    @NotNull
    private MultipartFile mask;
}
