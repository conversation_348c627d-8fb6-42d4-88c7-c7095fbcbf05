package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.TestResultDO;
import ai.conrain.aigc.platform.service.enums.TestGroupTypeEnum;
import ai.conrain.aigc.platform.service.enums.TestStatusEnum;
import ai.conrain.aigc.platform.service.enums.TestTypeEnum;
import ai.conrain.aigc.platform.service.model.query.TestResultQuery;
import ai.conrain.aigc.platform.dal.example.TestResultExample;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.TestItemGroupVO;
import ai.conrain.aigc.platform.service.model.vo.TestResultVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

import com.alibaba.fastjson.JSONObject;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_ROUND_ID;

/**
 * TestResultConverter
 *
 * @version TestResultService.java v 0.1 2024-12-19 01:24:06
 */
public class TestResultConverter {

    /**
     * DO -> VO
     */
    public static TestResultVO do2VO(TestResultDO from) {
        TestResultVO to = new TestResultVO();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setItemId(from.getItemId());
        to.setGroupId(from.getGroupId());
        to.setRoundId(from.getRoundId());
        to.setType(TestTypeEnum.getByCode(from.getType()));
        to.setStatus(TestStatusEnum.getByCode(from.getStatus()));
        to.setGroupType(TestGroupTypeEnum.getByCode(from.getGroupType()));
        to.setBatchId(from.getBatchId());
        to.setTaskId(from.getTaskId());
        to.setSeed(from.getSeed());
        to.setImageUrl(from.getImageUrl());
        to.setMiniImageUrl(from.getMiniImageUrl());
        to.setScore(from.getScore());
        to.setCaseId(from.getCaseId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setRequestParams(from.getRequestParams());
        // 处理扩展信息
        to.setExtInfo(StringUtils.isNotBlank(from.getExtInfo()) ? JSONObject.parseObject(from.getExtInfo()) : null);

        return to;
    }

    /**
     * VO -> DO
     */
    public static TestResultDO vo2DO(TestResultVO from) {
        TestResultDO to = new TestResultDO();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setItemId(from.getItemId());
        to.setGroupId(from.getGroupId());
        to.setRoundId(from.getRoundId());
        to.setType(from.getType().getCode());
        to.setStatus(from.getStatus().getCode());
        to.setGroupType(from.getGroupType().getCode());
        to.setBatchId(from.getBatchId());
        to.setTaskId(from.getTaskId());
        to.setSeed(from.getSeed());
        to.setImageUrl(from.getImageUrl());
        to.setMiniImageUrl(from.getMiniImageUrl());
        to.setScore(from.getScore());
        to.setCaseId(from.getCaseId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setRequestParams(from.getRequestParams());
        to.setExtInfo(from.getExtInfo() != null  ? from.getExtInfo().toJSONString() : null);

        return to;
    }

    /**
     * DO -> Query
     */
    public static TestResultQuery do2Query(TestResultDO from) {
        TestResultQuery to = new TestResultQuery();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setItemId(from.getItemId());
        to.setGroupId(from.getGroupId());
        to.setRoundId(from.getRoundId());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setBatchId(from.getBatchId());
        to.setTaskId(from.getTaskId());
        to.setSeed(from.getSeed());
        to.setImageUrl(from.getImageUrl());
        to.setMiniImageUrl(from.getMiniImageUrl());
        to.setScore(from.getScore());
        to.setCaseId(from.getCaseId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setRequestParams(from.getRequestParams());
        to.setExtInfo(StringUtils.isNotBlank(from.getExtInfo()) ? JSONObject.parseObject(from.getExtInfo()) : null);

        return to;
    }

    /**
     * Query -> DO
     */
    public static TestResultDO query2DO(TestResultQuery from) {
        TestResultDO to = new TestResultDO();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setItemId(from.getItemId());
        to.setGroupId(from.getGroupId());
        to.setRoundId(from.getRoundId());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setBatchId(from.getBatchId());
        to.setTaskId(from.getTaskId());
        to.setSeed(from.getSeed());
        to.setImageUrl(from.getImageUrl());
        to.setMiniImageUrl(from.getMiniImageUrl());
        to.setScore(from.getScore());
        to.setCaseId(from.getCaseId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setRequestParams(from.getRequestParams());
        // 处理扩展信息
        JSONObject extInfo = from.getExtInfo();
        if (extInfo != null && !extInfo.isEmpty()) {
            to.setExtInfo(extInfo.toJSONString());
        }

        return to;
    }

    /**
     * Query -> Example
     */
    public static TestResultExample query2Example(TestResultQuery from) {
        TestResultExample to = new TestResultExample();
        TestResultExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getPlanId())) {
            c.andPlanIdEqualTo(from.getPlanId());
        }
        if (!ObjectUtils.isEmpty(from.getItemId())) {
            c.andItemIdEqualTo(from.getItemId());
        }
        if (!ObjectUtils.isEmpty(from.getGroupId())) {
            c.andGroupIdEqualTo(from.getGroupId());
        }
        if (!ObjectUtils.isEmpty(from.getRoundId())) {
            c.andRoundIdEqualTo(from.getRoundId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getBatchId())) {
            c.andBatchIdEqualTo(from.getBatchId());
        }
        if (!ObjectUtils.isEmpty(from.getTaskId())) {
            c.andTaskIdEqualTo(from.getTaskId());
        }
        if (!ObjectUtils.isEmpty(from.getSeed())) {
            c.andSeedEqualTo(from.getSeed());
        }
        if (!ObjectUtils.isEmpty(from.getImageUrl())) {
            c.andImageUrlEqualTo(from.getImageUrl());
        }
        if (!ObjectUtils.isEmpty(from.getMiniImageUrl())) {
            c.andMiniImageUrlEqualTo(from.getMiniImageUrl());
        }
        if (!ObjectUtils.isEmpty(from.getScore())) {
            c.andScoreEqualTo(from.getScore());
        }
        if (!ObjectUtils.isEmpty(from.getCaseId())) {
            c.andCaseIdEqualTo(from.getCaseId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<TestResultVO> doList2VOList(List<TestResultDO> list) {
        return CommonUtil.listConverter(list, TestResultConverter::do2VO);
    }

    public static TestResultVO task2Result(CreativeTaskVO task, TestItemGroupVO group) {
        TestResultVO result = new TestResultVO();
        result.setId(task.getId());
        result.setPlanId(group.getPlanId());
        result.setItemId(group.getItemId());
        result.setGroupId(group.getId());
        Integer roundId = task.getExtInfo(KEY_TEST_ROUND_ID, Integer.class);
        result.setRoundId(roundId);
        result.setType(group.getType());
        result.setStatus(TestStatusEnum.COMPARING);
        result.setOperatorId(group.getOperatorId());
        result.setGroupType(group.getGroupType());

        result.setBatchId(task.getBatchId());
        result.setTaskId(task.getId());
        result.setImageUrl(task.getResultImages() == null ? null :task.getResultImages().get(0));
        result.setExtInfo(task.getExtInfo());
        return result;
    }
}