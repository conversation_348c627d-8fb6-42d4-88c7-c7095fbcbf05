package ai.conrain.aigc.platform.service.model.vo;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * ModelPointVO
 *
 * @version ModelPointService.java v 0.1 2024-06-21 12:01:15
 */
@Data
public class ModelPointVO implements Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 服装模型id */
    private Integer modelId;

    /** 图片点数 */
    private Integer point = 0;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

}
