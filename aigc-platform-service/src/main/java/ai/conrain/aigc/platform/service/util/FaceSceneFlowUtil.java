package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static ai.conrain.aigc.platform.service.util.FlowUtils.bypassDisabledNodes;
import static ai.conrain.aigc.platform.service.util.FlowUtils.removeDisabledNodes;

@Slf4j
public class FaceSceneFlowUtil {
    private static final String KEY_MD5 = "_MD5_";
    /** 缓存 */
    private static final ConcurrentMap<String, String> CACHE = new ConcurrentHashMap<>();

    /**
     * 对原流程进行修正
     * @param originFlow    原有流程
     * @param flowKey       流程key
     * @param isSwapFace    是否换脸
     * @param isCreateScene 是否生成背景
     * @param isUploadScene 是否上传背景
     * @return 修正后的流程
     */
    public static String correctFlow(String originFlow, String flowKey, boolean isSwapFace, boolean isCreateScene,
                                     boolean isUploadScene, Map<String, Object> context) {
        if (isSwapFace && !(isCreateScene || isUploadScene)) {
            JSONObject json = JSONObject.parseObject(originFlow);
            parseNumberControl(json, context);
            return json.toJSONString();
        }
        String key = buildKey(flowKey, isSwapFace, isCreateScene, isUploadScene);
        if (CACHE.containsKey(key)) {
            String currentMd5 = MD5Utils.md5Hex(originFlow, "UTF-8");
            String cachedMd5 = CACHE.get(KEY_MD5 + flowKey);
            if (StringUtils.equals(cachedMd5, currentMd5)) {
                log.info("【流程拆分】命中缓存，key={}", key);
                return CACHE.get(key);
            }
            log.info("【流程拆分】当前流程的md5和缓存中的md5不一致，重新初始化流程缓存，current={},cached={}", currentMd5,
                cachedMd5);
        }
        log.info("【流程拆分】当前缓存中不存在，开始初始化流程缓存，key={}", key);
        initCache(originFlow, flowKey);
        return CACHE.get(key);
    }

    /**
     * 初始化本地的流程缓存
     * @param originFlow    原流程
     * @param flowKey       流程key
     */
    private static void initCache(String originFlow, String flowKey) {
        long start = System.currentTimeMillis();
        Map<String, String> temp = new HashMap<>();

        // 遍历所有场景
        boolean[] boolArr = {true, false};
        for (boolean isSwapFace : boolArr) {
            for (boolean isCreateScene : boolArr) {
                for (boolean isUploadScene : boolArr) {
                    // 初始化参数
                    String key = buildKey(flowKey, isSwapFace, isCreateScene, isUploadScene);
                    Map<String, Object> context = new HashMap<>();
                    context.put("isSwapFace", isSwapFace);
                    context.put("isCreateScene", isCreateScene);
                    context.put("isUploadScene", isUploadScene);
                    // 创建每个条件下的流程
                    String flow = buildEachScene(originFlow, context);
                    // 缓存
                    temp.put(key, flow);
                    log.info("【流程拆分】完成初始化,{}的流程", key);
                }
           }
        }
        String md5 = MD5Utils.md5Hex(originFlow, "UTF-8");
        temp.put(KEY_MD5 + flowKey, md5);
        CACHE.clear();
        CACHE.putAll(temp);
        log.info("【流程拆分】初始化流程缓存完成，耗时={}ms", System.currentTimeMillis() - start);
    }

    /**
     * 初始化单个流程
     * @param originFlow    原流程
     * @param context       当前上下文
     * @return              flow
     */
    private static String buildEachScene(String originFlow, Map<String, Object> context) {
        JSONObject json = JSONObject.parseObject(originFlow);

        // 1. 处理prompt, 将不符合条件的节点删除
        JSONObject prompt = json.getJSONObject("prompt");
        // 遍历所有的节点, 并删除满足表达式解析后的disable = true的节点;
        removeDisabledNodes(prompt, context);

        // 2. 处理extra_data的...nodes节点，将不符合条件的节点 bypass，目前 0 是正常， 4 是 bypass ，由表达式控制是 0 还是 4
        JSONArray nodes = json.getJSONObject("extra_data").getJSONObject("extra_pnginfo").getJSONObject("workflow")
            .getJSONArray("nodes");
        bypassDisabledNodes(nodes, context);

        // 3. 需要解析为数字的参数
        parseNumberControl(prompt, context);

        return json.toJSONString();
    }

    /**
     * 解析 flow 中的 数字(int, long, double)
     * @param jsonObject 流程json
     * @param context 上下文
     */
    private static void parseNumberControl(JSONObject jsonObject, Map<String, Object> context) {
        if (jsonObject == null) {
            return;
        }

        Set<String> parseKeys = context.keySet();
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            if (value == null) {
                continue;
            }

            if (value instanceof JSONObject) {
                //递归遍历子节点，将所有包含变量的值进行解析
                parseNumberControl((JSONObject)value, context);
            } else if (value instanceof JSONArray) {

                JSONArray valueArr = (JSONArray)value;

                for (int i = 0; i < valueArr.size(); i++) {
                    Object item = valueArr.get(i);
                    if (item instanceof JSONObject) {
                        parseNumberControl((JSONObject)item, context);
                    } else if (item instanceof String) {
                        String valueStr = (String)item;
                        if (needParse(valueStr, parseKeys)) {
                            String result = FreemarkerUtils.parse(valueStr, context);
                            //如果是数字类型，则转换成数字
                            if (org.apache.commons.lang3.StringUtils.contains(valueStr, "?number")) {
                                if (org.apache.commons.lang3.StringUtils.contains(result, ".")) {
                                    valueArr.set(i, Double.parseDouble(result));
                                } else {
                                    valueArr.set(i, Long.parseLong(result));
                                }
                            } else {
                                valueArr.set(i, result);
                            }
                        }
                    }
                }
            } else if (value instanceof String) {
                String valueStr = (String)value;
                if (needParse(valueStr, parseKeys)) {
                    String result = FreemarkerUtils.parse(valueStr, context);
                    //如果是数字类型，则转换成数字
                    if (org.apache.commons.lang3.StringUtils.contains(valueStr, "?number")) {
                        if (org.apache.commons.lang3.StringUtils.contains(result, ".")) {
                            jsonObject.put(key, Double.parseDouble(result));
                        } else {
                            jsonObject.put(key, Long.parseLong(result));
                        }
                    }else {
                        jsonObject.put(key, result);
                    }
                }
            }
        }
    }

    /**
     * 判断是否需要解析
     *
     * @param value value值
     * @param keys  key列表
     * @return true：需要
     */
    private static boolean needParse(String value, Set<String> keys) {
        if (org.apache.commons.lang3.StringUtils.isBlank(value)) {
            return false;
        }

        if (!org.apache.commons.lang3.StringUtils.contains(value, "${")) {
            return false;
        }

        return keys.stream().anyMatch(key -> org.apache.commons.lang3.StringUtils.contains(value, key));
    }

    /**
     * 构建key
     * @param flowKey       流程key
     * @param isSwapFace    是否需要换脸
     * @param isCreateScene 是否是生成背景
     * @param isUploadScene 是否是上传背景
     * @return              key
     */
    private static String buildKey(String flowKey, boolean isSwapFace, boolean isCreateScene, boolean isUploadScene) {
        return String.format("%s-%s-%s-%s", flowKey, isSwapFace, isCreateScene, isUploadScene);
    }
}
