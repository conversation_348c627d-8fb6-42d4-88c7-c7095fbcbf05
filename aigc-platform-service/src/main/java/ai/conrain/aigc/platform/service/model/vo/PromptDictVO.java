package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.common.ModifyTimeClz;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * PromptDictVO
 *
 * @version PromptDictService.java v 0.1 2024-11-15 07:24:51
 */
@Data
public class PromptDictVO implements Serializable, ModifyTimeClz {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 名词 */
    private String word;

    /** 对应的prompt */
    private String prompt;

    /** 类型 */
    private DictTypeEnum type;

    /** 展示图url */
    private String showImage;

    /** 标签列表，多个以逗号隔开 */
    private List<String> tags;

    /** 备注 */
    private String memo;

    /** 用户id */
    private Integer userId;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

}
