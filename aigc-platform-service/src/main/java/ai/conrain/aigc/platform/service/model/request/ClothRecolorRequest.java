package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

//服装换色入参
@Data
public class ClothRecolorRequest implements CreativeRequest {
    //原图地址
    @NotBlank
    private String originImgUrl;
    //蒙版原图地址
    @NotBlank
    private String originMaskImgUrl;
    //蒙版地址
    @NotBlank
    private String maskImgUrl;
    //目标颜色,#ffffff格式
    @NotBlank
    private String targetHexColor;
    //是否智能换色
    private Boolean smartRecolorMode;
}
