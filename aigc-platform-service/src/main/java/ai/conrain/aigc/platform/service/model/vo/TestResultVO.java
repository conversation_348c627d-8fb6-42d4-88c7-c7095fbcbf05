package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.TestGroupTypeEnum;
import ai.conrain.aigc.platform.service.enums.TestStatusEnum;
import ai.conrain.aigc.platform.service.enums.TestTypeEnum;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;

/**
 * TestResultVO
 *
 * @version TestResultService.java v 0.1 2024-12-19 01:24:06
 */
@Data
public class TestResultVO implements Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 测试计划id */
    private Integer planId;

    /** 测试项目id */
    private Integer itemId;

    /** 分组id */
    private Integer groupId;

    /** 轮次id */
    private Integer roundId;

    /** 类型，TRAIN、CREATIVE */
    private TestTypeEnum type;

    /** 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED */
    private TestStatusEnum status;

    private TestGroupTypeEnum groupType;

    /** 批次id */
    private Integer batchId;

    /** 任务id */
    private Integer taskId;

    /** 种子 */
    private String seed;

    /** 图片地址 */
    private String imageUrl;

    /** 缩略图地址 */
    private String miniImageUrl;

    /** 得分,正负 */
    private Boolean score;

    /** 图库id */
    private Integer caseId;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /** 请求参数 */
    private String requestParams;

    /** 扩展信息 */
    private JSONObject extInfo;

    public String getStatusName() {
        return status != null ? status.getDesc() : null;
    }
}
