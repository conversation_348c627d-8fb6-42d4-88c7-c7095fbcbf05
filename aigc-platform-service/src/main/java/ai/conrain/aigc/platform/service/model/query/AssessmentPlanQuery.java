package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * AssessmentPlanQuery
 *
 * @version AssessmentPlanService.java v 0.1 2025-05-22 07:34:51
 */
@Data
public class AssessmentPlanQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 结算主体类型 */
    private String principalType;

    /** 结算主体 id */
    private Integer principalId;

    /** 考核类型 */
    private String type;

    /** 考核任务状态 */
    private String status;

    /** 考核计划开始日期 */
    private Date planFromDate;

    /** 考核计划结束日期 */
    private Date planEndDate;

    /** 创建人用户id */
    private Integer creatorUserId;

    /** 修改人用户id */
    private Integer modifyUserId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;


    /** 考核指标 */
    private String kpiTarget;

    /** 实际完成情况 */
    private String kpiActual;

    /** 扩展字段 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
