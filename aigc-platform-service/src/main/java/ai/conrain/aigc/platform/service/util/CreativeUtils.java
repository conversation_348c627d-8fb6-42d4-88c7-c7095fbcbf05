/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import org.apache.commons.lang3.StringUtils;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LENS;

/**
 * 创作工具类
 *
 * <AUTHOR>
 * @version : CreativeUtils.java, v 0.1 2025/7/23 14:45 renxiao.wu Exp $
 */
public abstract class CreativeUtils {
    /**
     * 格式化服装激活词
     *
     * @param modelTags    服装激活词
     * @param sceneElement 场景元素
     * @return 格式化后的服装激活词
     */
    public static String formatModelTags(String modelTags, CreativeElementVO sceneElement) {
        if (!ElementUtils.isStyleScene(sceneElement)) {
            return modelTags;
        }

        String activateKey = StringUtils.contains(modelTags, "(linrun2111:1.3)") ? "(linrun2111:1.3), "
            : (StringUtils.contains(modelTags, "linrun2111") ? "linrun2111, " : "");

        String sceneLens = sceneElement.getExtInfo(KEY_LENS, String.class);
        if (StringUtils.contains(modelTags, "mid shot") || StringUtils.contains(modelTags, "close shot")) {
            sceneLens = StringUtils.replace(sceneLens, CameraAngleEnum.UPPER_BODY.getCode(), "");
            sceneLens = StringUtils.replace(sceneLens, CameraAngleEnum.LOWER_BODY.getCode(), "");
            sceneLens = StringUtils.replace(sceneLens, ",,", ",");

            modelTags = activateKey + sceneLens + (StringUtils.contains(modelTags, "mid shot") ? "mid shot," : "") + (
                StringUtils.contains(modelTags, "close shot") ? "close shot," : "");
        } else {
            modelTags = activateKey + sceneLens;
        }

        //去掉原有场景的角度
        sceneElement.getExtInfo().remove(KEY_LENS);

        return modelTags;
    }
}
