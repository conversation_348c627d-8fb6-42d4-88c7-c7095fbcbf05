package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

import java.io.Serializable;

/**
 * TrainParamQuery
 *
 * @version TrainParamService.java v 0.1 2024-11-19 08:51:42
 */
@Data
public class TrainParamQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Integer id;

    /** 关联的训练计划ID */
    private Integer trainPlanId;

    /** 训练分辨率 */
    private String trainResolution;

    /** 学习内容或风格 */
    private String contentOrStyle;

    /** 关联的loraModelId */
    private Integer relatedLoraModelId;

    /** 关联的loraModelName */
    private String relatedLoraModelName;

    /** Rank值 */
    private Integer loraRank;

    /** Alpha值 */
    private Integer alpha;

    /** 训练步数 */
    private Integer trainStep;

    /** 学习率 */
    private BigDecimal lr;

    /** Dropout值 */
    private BigDecimal dropout;

    /** 扩展信息 */
    private String extInfo;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
