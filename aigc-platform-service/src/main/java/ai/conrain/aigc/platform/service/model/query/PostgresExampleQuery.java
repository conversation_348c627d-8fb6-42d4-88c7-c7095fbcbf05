package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * PostgresExampleQuery
 *
 * @version PostgresExampleService.java v 0.1 2025-07-22 04:16:39
 */
@Data
public class PostgresExampleQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private Long id;

    /** name */
    private String name;

    /** 创建时间 */
    private Date createTime;

    /** 创建时间 */
    private Date modifyTime;


    /** clothVector */
    private Object clothVector;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
