package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * CodeQuery
 *
 * @version CodeService.java v 0.1 2025-05-19 02:24:41
 */
@Data
public class CodeQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 码值，全局唯一 */
    private String code;

    /** 码类型，registerPromotion:推广注册 */
    private String codeType;

    /** 码状态，valid:有效|invalid:已失效 */
    private String codeStatus;

    /** 码的信息 */
    private String codeInfo;

    /** 关联的用户 */
    private Integer relatedUserId;

    /** 创建人主账号id */
    private Integer creatorMasterId;

    /** 创建人id */
    private Integer creatorId;

    /** 最近修改人id */
    private Integer modifierId;

    /** 预留扩展 */
    private String extInfo;


    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
