package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 测试结果扩展信息枚举
 */
@Getter
public enum TestResultExtInfoEnum {

    /**
     * 模特相似度
     */
   FACE_SIMILARITY("faceSimilarity","人脸相似度"),
   /**
    * 场景相似度
    */
   SCENE_SIMILARITY("sceneSimilarity","场景相似度"),
   /**
    * 服饰相似度
    */
   MATERIAL_SIMILARITY("materialSimilarity","材质相似度");


;
    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    TestResultExtInfoEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据 Code 获取枚举信息
     *
     * @param code 类型编码
     * @return 枚举信息
     */
    public static TestResultExtInfoEnum getByCode(String code) {
        for (TestResultExtInfoEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }
}
