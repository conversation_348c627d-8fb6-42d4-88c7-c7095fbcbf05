package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * PrincipalInfoQuery
 *
 * @version PrincipalInfoService.java v 0.1 2025-06-07 06:21:35
 */
@Data
public class PrincipalInfoQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 主体类型 */
    private String principalType;

    /** 关联的用户id */
    private Integer principalId;

    /** key */
    private String infoKey;

    /** 创建人用户id */
    private Integer creatorUserId;

    /** 修改人用户id */
    private Integer modifyUserId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;


    /** 信息 */
    private String infoValue;

    /** 扩展信息 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
