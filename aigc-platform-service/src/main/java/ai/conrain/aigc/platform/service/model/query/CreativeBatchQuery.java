package ai.conrain.aigc.platform.service.model.query;

import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.validation.SimpleDate;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * CreativeBatchQuery
 *
 * @version CreativeBatchService.java v 0.1 2024-05-10 10:13:22
 */
@Data
public class CreativeBatchQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 批次id */
    private Integer id;

    /** 模型id */
    private Integer modelId;

    /** 模型类型，SYSTEM、CUSTOM */
    private String modelType;

    /** 类型，CREATE_IMAGE、REPAIR_HANDS */
    private String type;

    /** 归属主账号id */
    private Integer userId;

    /** 图片url */
    private String showImage;

    /** 图片比例，3:4、1:1等 */
    private String imageProportion;

    /** 批次数量 */
    private Integer batchCnt;

    /** 批次数量大于, 默认大于 0, 如果设置了 batchCnt, 则覆盖此参数 */
    private Integer batchCntGreaterThan = 0;

    /** ComfyUI返回的唯一标识 */
    private String promptId;

    /** 结果图片路径 */
    private String resultPath;

    /** 状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED */
    private String status;

    private List<String> statusList;

    /** 操作者id */
    private Integer operatorId;

    private List<Integer> operatorIds;

    /** 创建时间 */
    private Date createTime;

    /** 创建时间在该时间之前 */
    private Date createTimeBefore;

    /** 修改时间 */
    private Date modifyTime;

    /** 标题 */
    private String title;

    /** 业务类型 */
    private String bizType;

    /** aigc请求参数 */
    private String aigcRequest;

    /** 结果图片url列表 */
    private String resultImages;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    //起始日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateFrom;

    //截止日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateTo;

    private String nickLike;

    private List<Integer> userIds;

    private List<Integer> userIdsNotIn;

    private RoleTypeEnum queryRoleType;

    private String bizTag;

    private Boolean isSelectNotSyncData;

    private String bizTagOrOperator;

    private boolean onlyShowDislike = false;

    private boolean onlyShowLike = false;

    private List<String> typeNotIn;

    private List<String> typeList;

    private Date endDate;

    private String relatedOperator;

    private Integer enabledUserModel;

    private boolean onlyShowVIP = false;

    private boolean onlyShowNewModel = false;

    private boolean onlyShowRefine = false;

    private Integer relateDistOpUserId;

    private String garmentType;

    private List<String> garmentTypeList;

    private String customerType;

    private boolean onlyShowDemo = false;

    private List<Integer> elementIds;

    private Integer elementId;

    private String sceneId;

    private Date startTimeBefore;

    //临期交付的创作
    private boolean onlyNearingDelivery;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    //只查有过下载的
    private boolean onlyDownloaded;

    /** 仅展示处理中的 */
    private boolean onlyShowProcessing;

    /** 仅展示用户创作的 */
    private boolean onlyShowUserCreative;

    /** 业务标签不等于 */
    private List<String> bizTagNotIn;

    /** 业务标签为空 */
    private boolean bizTagNull;

    /** 姿势 Id 集合 */
    private List<Integer> postureIdList;

    /** 是否与我有关的 */
    private boolean relatedToMe = false;

}
