package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;

import java.util.Date;
import java.util.List;

import java.io.Serializable;

/**
 * TestPlanQuery
 *
 * @version TestPlanService.java v 0.1 2024-12-19 01:24:06
 */
@Data
public class TestPlanQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 测试计划名称 */
    private String name;

    /** 类型，TRAIN、CREATIVE */
    private String type;

    /** 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED */
    private String status;

    /** 备注 */
    private String memo;

    /** 归属用户id */
    private Integer userId;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    private List<String> statusList;

    private String nameLike;

}
