package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;

import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * UserProfileQuery
 *
 * @version UserProfileService.java v 0.1 2024-06-07 07:47:21
 */
@Data
public class UserProfileQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 关联的用户id */
    private Integer uid;

    /** 关联的用户ids */
    private List<Integer> uids;

    /** 属性key */
    private String profileKey;

    /** 属性keys */
    private List<String> profileKeys;

    /** 属性val */
    private String profileVal;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;


    /** 属性值列表 */
    private List<String> profileValList;

}
