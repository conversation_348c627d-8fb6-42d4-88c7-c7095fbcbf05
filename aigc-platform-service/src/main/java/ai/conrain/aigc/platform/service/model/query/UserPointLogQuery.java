package ai.conrain.aigc.platform.service.model.query;

import ai.conrain.aigc.platform.service.validation.SimpleDate;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * UserPointLogQuery
 *
 * @version UserPointLogService.java v 0.1 2024-06-21 04:14:12
 */
@Data
public class UserPointLogQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    private List<Integer> userIds;

    /** 类型，充值单、服装建模、套餐外创、创作退回作等 */
    private String type;

    private List<String> typeList;

    /** 关联id */
    private Integer relatedId;

    /** 算力点 */
    private Integer point;

    //是否需要消耗point的记录
    private boolean consumeMusePoint;

    /** 赠送点数 */
    private Integer givePoint;

    /** 体验算力点 */
    private Integer experiencePoint;

    /** 服装套餐内算力点 */
    private Integer modelPoint;

    /** 操作id */
    private Integer operatorId;

    /** 备注 */
    private String memo;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 扩展信息 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    //起始日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateFrom;

    //截止日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateTo;

}
