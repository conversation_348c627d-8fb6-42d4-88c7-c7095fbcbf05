package ai.conrain.aigc.platform.service.model.query;

import ai.conrain.aigc.platform.service.validation.SimpleDate;

import lombok.Data;

@Data
public class BasePageQuery {

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    //起始日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateFrom;

    //截止日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateTo;
}
