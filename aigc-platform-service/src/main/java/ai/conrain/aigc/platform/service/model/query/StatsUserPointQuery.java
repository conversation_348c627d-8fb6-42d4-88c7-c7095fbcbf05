package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

import java.io.Serializable;

/**
 * StatsUserPointQuery
 *
 * @version StatsUserPointService.java v 0.1 2025-04-11 10:09:06
 */
@Data
public class StatsUserPointQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 用户昵称 */
    private String nickNameLike;

    /** 所属渠道商 */
    private String distributorCorpNameLike;

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    private String statsDate;

    /** 统计日期下界: 格式为yyyy-MM-dd */
    private String statsDateLower;

    /** 统计日期上届: 格式为yyyy-MM-dd */
    private String statsDateUpper;

    /** 消耗的算力点 */
    private Integer pointConsumed;

    /** 消耗算力点的下限 */
    private Integer pointConsumedLower;

    /** 消耗算力点的上限 */
    private Integer pointConsumedUpper;

    /** 消耗的赠送点 */
    private Integer givePointConsumed;

    /** 消耗的赠送点的下限 */
    private Integer givePointConsumedLower;

    /** 消耗的赠送点的上限 */
    private Integer givePointConsumedUpper;

    /** 消耗的体验点 */
    private Integer expPointConsumed;

    /** 消耗的体验点的下限 */
    private Integer expPointConsumedLower;

    /** 消耗的体验点的上限 */
    private Integer expPointConsumedUpper;

    /** 消耗的套内点 */
    private Integer modelPointConsumed;

    /** 消耗的套内点的下限 */
    private Integer modelPointConsumedLower;

    /** 消耗的套内点的上限 */
    private Integer modelPointConsumedUpper;

    /** 充值金额 */
    private BigDecimal rechargeAmount;

    /** 充值金额的下限 */
    private BigDecimal rechargeAmountLower;

    /** 充值金额的上限 */
    private BigDecimal rechargeAmountUpper;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
