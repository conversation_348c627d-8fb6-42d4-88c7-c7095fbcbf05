package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.alibaba.fastjson.JSONObject;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * FixedCreativeTemplateVO
 *
 * @version FixedCreativeTemplateService.java v 0.1 2025-05-27 05:39:17
 */
@Data
public class FixedCreativeTemplateVO implements Serializable, IExtModel {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 模板名称 */
    private String templateName;

    /** 创建时间（收藏时间） */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 更新时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /** 模板图片列表 */
    private String templateList;

    /** 扩展信息 */
    private JSONObject extInfo;


    /** 参考图信息列表 */
    @NotEmpty
    private List<ReferenceInfo> referenceInfoList;


    /** 参考图信息 */
    @Data
    public static class ReferenceInfo {

        /** 参考图图片 */
        @NotBlank
        private String imageUrl;

        /** 参考图配置 */
        @NotBlank
        private JSONObject referenceConfig;

        /** 背景标签 */
        private String backTags;

        /** 背景扩展标签 */
        private String backExtTags;

        /** LoraId */
        private Integer loraId;

        /**
         * lora地址
         */
        private String loraPath;
    }

}
