package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class PartialRedrawRequest implements CreativeRequest{
    private static final long serialVersionUID = 2060239558687266585L;
    /** 任务id */
    private Integer taskId;

    /** 服装id */
    private Integer modelId;

    /** 原始图片url */
    @NotBlank
    private String originImage;

    /** 重绘内容描述 */
    @NotBlank
    private String redrawDesc;

    /** 图片数量 */
    @NotNull
    private Integer imageNum;

    @NotNull
    private MultipartFile mask;
}
