package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.model.biz.WorkflowOpenScopeModel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * ComfyuiWorkflowTemplateActiveVersionVO
 *
 * @version ComfyuiWorkflowTemplateActiveVersionService.java v 0.1 2025-06-30 05:46:14
 */
@Data
public class ComfyuiWorkflowTemplateActiveVersionVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 主键id */
	private Integer id;

	/** 模板key */
	private String templateKey;

	/** 模板描述 */
	private String templateDesc;

	/** 模板版本，如20250610.1 */
	private String activeVersion;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 更新时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

	/** 创建人id */
	private Integer createBy;

	/** 修改人id */
	private Integer modifyBy;

	/** 测试模板版本，如20250610.1 */
	private String testVersion;

	/** 测试模板开放范围 */
	private WorkflowOpenScopeModel testOpenScope;

}
