package ai.conrain.aigc.platform.service.model.request;

import java.util.List;

import ai.conrain.aigc.platform.service.model.biz.MaskMergeModel;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
public class RepairDetailRequest implements MaskMergeCreativeRequest{
    private static final long serialVersionUID = -4440659260651418033L;

    @NotBlank
    private String image;

    /** 服装图片 */
    @NotBlank
    private String clothImage;

    /** 出图数量 */
    @NotNull
    private Integer imageNum;

    @Valid
    @NotEmpty
    private List<MaskMergeModel> maskMergeModels;
}
