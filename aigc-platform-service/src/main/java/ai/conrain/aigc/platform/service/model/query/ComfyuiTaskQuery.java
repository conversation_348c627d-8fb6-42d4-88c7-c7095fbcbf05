package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * ComfyuiTaskQuery
 *
 * @version ComfyuiTaskService.java v 0.1 2024-05-30 10:53:32
 */
@Data
public class ComfyuiTaskQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 模型训练任务id */
    private Integer id;

    /** 归属主账号id */
    private Integer userId;

    /** 操作人账号id */
    private Integer operatorId;

    /** 任务类型, cutout/mark-label/lora */
    private String taskType;

    /** 任务状态, QUEUED/RUNNING/COMPLETED/FAILED/UNKNOWN/NONE */
    private String taskStatus;

    /** 自定义请求参数 */
    private String reqParams;

    /** ComfyUI返回的唯一标识 */
    private String promptId;

    /** 扩展 */
    private String extInfo;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** comfyui api请求报文 */
    private String comfyuiRequest;

    /** 结果详情 */
    private String retDetail;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    private List<String> taskTypeList;
}
