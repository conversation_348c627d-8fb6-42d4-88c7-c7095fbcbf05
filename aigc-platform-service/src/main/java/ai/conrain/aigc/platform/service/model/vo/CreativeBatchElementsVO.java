package ai.conrain.aigc.platform.service.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * CreativeBatchElementsVO
 *
 * @version CreativeBatchElementsService.java v 0.1 2024-05-08 03:35:57
 */
@Data
public class CreativeBatchElementsVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 批次id */
	private Integer id;

	/** 模型id */
	private Integer batchId;

	/** 元素id */
	private Integer elementId;

	private String elementKey;

	/** 归属主账号id */
	private Integer userId;

	private Integer operatorId;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

}
