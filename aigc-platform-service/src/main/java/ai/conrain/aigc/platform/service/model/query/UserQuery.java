package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * UserQuery
 *
 * @version UserService.java v 0.1 2024-01-20 01:21:36
 */
@Data
public class UserQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 用户id */
    private Integer id;

    private List<Integer> ids;

    /** 昵称 */
    private String nickName;

    private String nickNameLike;

    private String corpNameLike;

    /** 真实姓名 */
    private String realName;

    /** 登录id */
    private String loginId;

    /** 登录密码 */
    private String pswd;

    /** 手机号码 */
    private String mobile;

    private String mobileLike;

    /** 角色类型 */
    private String roleType;

    private List<String> roleTypeList;

    private String customRole;

    private List<String> customRoleList;

    private List<String> exceptCustomRoleList;

    /** 公司名（当前用户创建时关联的企业名）*/
    private String corpName;

    /** 公司组织id（当前用户创建时关联的企业组织id）*/
    private Integer corpOrgId;

    /** 用户类型，MASTER、SUB */
    private String userType;

    /** 主账号id */
    private Integer masterId;

    /** 状态，ENABLED、DISABLED */
    private String status;

    /** 操作者id */
    private Integer operatorId;

    /** 注册来源 */
    private String registerFrom;

    /** 备注 */
    private String memo;

    private String memoLike;

    /** 登录失败次数 */
    private Integer loginFailCount;

    /** 最后登录时间 */
    private Date lastLoginTime;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    private boolean needPoint;

    private List<Integer> idNotIn;

    // yyyyMMDD
    private String lastVisitDateBefore;

    // 关联的渠道商id
    private Integer relatedDistributorMasterUserId;
    // 关联的渠道销售id
    private Integer relatedDistributorSalesUserId;
    // 关联的工程师id
    private Integer relatedPromptEngineerUserId;

    // 只查需要关联工程师的（充值3999及以上）用户
    private boolean onlyNeedPromptEngineer;

    // 创建时间在60天前的用户
    private Date before60Days;

    /** 父级 id 集合 */
    private List<Integer> masterIdList;

    /**
     * 是否添加虚拟用户条件
     */
    private Boolean virtualMemo = Boolean.FALSE;

    /**
     * 是否添加已退款用户条件
     */
    private Boolean refundedMemo = Boolean.FALSE;

    /**
     * 是否已签约
     */
    private boolean onlyNeedContract;

}
