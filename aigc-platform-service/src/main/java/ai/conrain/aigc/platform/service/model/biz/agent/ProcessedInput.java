package ai.conrain.aigc.platform.service.model.biz.agent;

import com.pgvector.PGvector;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ProcessedInput {
    private ClothDescription clothDescription;
    private List<UserRefImgDescription> userRefImgDescriptions;
    private Map<String, Double> dynamicWeights;
    // 优化性能：预计算的各维度平均向量，避免在MMR算法中重复遍历所有参考图
    private Map<String, PGvector> averageDimensionVectors;
}
