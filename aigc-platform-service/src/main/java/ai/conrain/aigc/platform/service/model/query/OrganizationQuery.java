package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * OrganizationQuery
 *
 * @version OrganizationService.java v 0.1 2024-07-12 04:26:40
 */
@Data
public class OrganizationQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 组织ID */
    private Integer id;

    /** 父级组织ID，根组织为0 */
    private Integer parentId;

    /** 是否根结点组织，0不是，1是 */
    private Boolean root;

    /** 根组织ID，根组织ID等于ID,根组织有且只有一个 */
    private Integer rootId;

    /** 是否叶子结点组织，0不是，1是 */
    private Boolean leaf;

    /** 组织类型,OrganizationTypeEnum */
    private String orgType;

    /** 组织名称 */
    private String name;

    /** 组织标签，作为组织类型的补充，预留 */
    private String tags;

    /** 组织层级，根组织为0，根组织下有1级组织，1级组织下有2级组织，以此类推 */
    private Integer orgLevel;

    /** 创建者主账号id */
    private Integer creatorMasterUserId;

    /** 创建人角色类型，DISTRIBUTOR：渠道商 */
    private String creatorUserRoleType;

    /** 创建人操作员账号id */
    private Integer creatorOperatorUserId;

    /** 最近修改人的操作员账号id */
    private Integer modifierOperatorUserId;

    /** 扩展信息 */
    private String extInfo;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /** 企业id */
    private List<Integer> orgIdList;
}
