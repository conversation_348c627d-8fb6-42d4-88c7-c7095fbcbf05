package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * ShowCaseQuery
 *
 * @version ShowCaseService.java v 0.1 2024-11-25 05:41:27
 */
@Data
public class ShowCaseQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 案例名称 */
    private String name;

    /** 类型，IMAGE、VIDEO */
    private String type;

    /** 结果图/视频地址url */
    private String mainUrl;

    /** 展示图url */
    private String showImage;

    /** 模特id */
    private Integer faceId;

    /** 场景id */
    private Integer sceneId;

    /** 服装模型id */
    private Integer modelId;

    /** 服装url */
    private String modelUrl;

    /** 排序 */
    private Integer order;

    /** 标签列表，多个以逗号隔开 */
    private String tags;

    /** 状态，ENABLED、DISABLED */
    private String status;

    /** 备注 */
    private String memo;

    /** 用户id */
    private Integer userId;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 服装搭配信息 */
    private String clothCollocation;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
