package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * TrainPlanQuery
 *
 * @version TrainPlanService.java v 0.1 2024-11-19 08:34:41
 */
@Data
public class TrainPlanQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Integer id;

    /** 训练计划名称 */
    private String planName;

    /** 原始克隆服装ID */
    private Integer clothingId;

    /** 原始克隆服装名称 */
    private String clothingName;

    /** 备注信息 */
    private String remarks;

    /** 每组图片数量 */
    private Integer imagesPerCombination;

    /** 模特 ID 列表，以逗号分隔 */
    private String faceModels;

    /** 场景 ID 列表，以逗号分隔 */
    private String scenes;

    /** 出图尺寸列表，以逗号分隔 */
    private String sizes;

    /** 扩展信息 */
    private String extInfo;

    /** 创建者用户ID */
    private Integer creatorUserId;

    /** 创建者用户名 */
    private String creatorUserName;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
