package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;

/**
 * TestResultQuery
 *
 * @version TestResultService.java v 0.1 2024-12-19 01:24:06
 */
@Data
public class TestResultQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 测试计划id */
    private Integer planId;

    /** 测试项目id */
    private Integer itemId;

    /** 分组id */
    private Integer groupId;

    /** 轮次id */
    private Integer roundId;

    /** 类型，TRAIN、CREATIVE */
    private String type;

    /** 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED */
    private String status;

    /** 批次id */
    private Integer batchId;

    /** 任务id */
    private Integer taskId;

    /** 种子 */
    private String seed;

    /** 图片地址 */
    private String imageUrl;

    /** 缩略图地址 */
    private String miniImageUrl;

    /** 得分,正负 */
    private Boolean score;

    /** 图库id */
    private Integer caseId;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 请求参数 */
    private String requestParams;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;


    /** 扩展信息 */
    private JSONObject extInfo;

}
