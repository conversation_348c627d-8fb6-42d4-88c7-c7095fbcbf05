package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * UserPointQuery
 *
 * @version UserPointService.java v 0.1 2024-05-15 10:58:45
 */
@Data
public class UserPointQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 算力点 */
    private Integer point;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /** 用户id */
    private List<Integer> userIdList;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

}
