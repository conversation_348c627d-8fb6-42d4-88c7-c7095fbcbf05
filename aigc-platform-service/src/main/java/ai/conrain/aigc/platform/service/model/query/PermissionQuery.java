package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PermissionQuery
 *
 * @version PermissionService.java v 0.1 2024-01-20 01:21:37
 */
@Data
public class PermissionQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 执行动作 */
    private String action;

    /** 权限名称 */
    private String name;

    /** 权限配置 */
    private String config;

    /** 是否允许子账号执行 */
    private Boolean allowedSub;

    /** 备注 */
    private String memo;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
