package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * ShootingStyleQuery
 *
 * @version ShootingStyleService.java v 0.1 2025-07-04 05:16:37
 */
@Data
public class ShootingStyleQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 一级分类名称 */
    private String type1Name;

    /** 二级分类名称 */
    private String type2Name;

    /** 英文名称 */
    private String type2EnName;

    /** 代表性品牌 */
    private String representativeBrand;

    /** 经典元素 */
    private String classicElements;

    /** 创建账号id */
    private Integer createBy;

    /** 修改账号id */
    private Integer modifyBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 示例图片地址列表，jsonArray格式 */
    private String examImageUrls;

    /** 风格描述 */
    private String styleDesc;

    /** 模特类型，适合的男模特/女模特类型，男模标签列表，女模标签列表 */
    private String modelTags;

    /** 扩展 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
