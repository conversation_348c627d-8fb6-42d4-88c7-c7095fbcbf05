package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;

/**
 * PromptModificationLogVO
 *
 * @version PromptModificationLogService.java v 0.1 2025-03-24 05:26:36
 */
@Data
public class PromptModificationLogVO implements IExtModel,Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 主键ID */
	private Integer id;

	/** 模块代码，如A、B、C */
	private String moduleCode;

	/** 模块主类型，如服装类型、人脸类型、场景类型 */
	private String moduleType;

	/** 父级元素id */
	private Integer parentElementId;

	/** 操作人ID */
	private Integer operatorId;

	/** 操作人姓名 */
	private String operatorName;

	/** 操作时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date operationTime;

	/** 旧数据（修改前的数据 json 格式） */
	private JSONObject oldFieldValue;

	/** 新数据（本次修改的数据 json 格式） */
	private JSONObject newFieldValue;

	/** 固定属性JSON，记录固定属性值，如{demo:value} */
	private JSONObject fixedAttrs;

	/** 扩展信息，存储额外的JSON格式数据 */
	private JSONObject extInfo;

	/** 备注信息 */
	private String remark;

}
