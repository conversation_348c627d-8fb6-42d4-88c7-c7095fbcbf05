package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * WorkflowTaskQuery
 *
 * @version WorkflowTaskService.java v 0.1 2025-04-02 04:59:35
 */
@Data
public class WorkflowTaskQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Integer id;

    /** 关联的业务id */
    private Integer bizId;

    /** 任务类型 */
    private String type;

    /** 任务类型 */
    private List<String> types;

    /** 操作人 */
    private Integer operatorId;

    /** 状态 */
    private String status;

    /** 是否审核 */
    private Boolean reviewed;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 拓展字段 */
    private String meta;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /** 是否当天的 */
    private boolean onlyToday;

    /** 操作人id集合 */
    private List<Integer> operatorIds;

    /** 业务id集合 */
    private List<Integer> bizIds;
}
