package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * CommonTaskQuery
 *
 * @version CommonTaskService.java v 0.1 2024-12-31 09:04:22
 */
@Data
public class CommonTaskQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 任务id */
    private Integer id;

    private List<Integer> idList;

    /** 归属主账号id */
    private Integer userId;

    /** 操作员/子账号id */
    private Integer operatorId;

    /** 任务类型, video_generation/extend_video/lip_sync等 */
    private String taskType;

    private List<String> taskTypeList;

    /** 任务状态, INIT/PENDING/RUNNING/COMPLETED/FAILED/CANCELED */
    private String taskStatus;

    private List<String> taskStatusList;

    /** 第三方平台名称 */
    private String outTaskPlatform;

    /** 第三方平台任务id */
    private String outTaskId;

    /** 任务状态 */
    private String outTaskStatus;

    /** 自定义请求参数 */
    private String reqBizParams;

    /** 关联业务类型 */
    private String relatedBizType;

    /** 关联业务id */
    private String relatedBizId;

    /** 扩展 */
    private String extInfo;

    /** 任务开始时间 */
    private Date taskStartTime;

    /** 任务结束时间 */
    private Date taskEndTime;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;


    /** 完整请求报文，主要用于请求外部http或comfyui服务的重试 */
    private String completeRequest;

    /** 结果详情 */
    private String retDetail;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
