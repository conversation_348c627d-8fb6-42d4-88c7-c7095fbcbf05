package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class BrandTryOnRequest implements CreativeRequest {
    private static final long serialVersionUID = -4440659260651418033L;

    private String key;

    @NotEmpty
    private List<ImageMaskModel> referenceImages;

    /** 服装图片 */
    @NotBlank
    private String clothImage;

    @NotBlank
    private String clothMaskUrl;

    /** 出图数量 */
    @NotNull
    private Integer imageNum;

}
