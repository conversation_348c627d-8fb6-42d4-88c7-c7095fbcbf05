/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import java.util.Optional;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.IdModel;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;

/**
 * 带扩展的模型接口
 *
 * <AUTHOR>
 * @version : IExtModel.java, v 0.1 2024/8/5 21:22 renxiao.wu Exp $
 */
public interface IExtModel extends IdModel {

    /**
     * 获取扩展信息
     *
     * @return 扩展信息
     */
    JSONObject getExtInfo();

    /**
     * 设置扩展信息
     *
     * @param extInfo 扩展信息
     */
    void setExtInfo(JSONObject extInfo);

    /**
     * 用户id
     *
     * @return 用户id
     */
    default Integer getUserId() {
        return OperationContextHolder.getMasterUserId();
    }

    /**
     * 当前是否处理中
     *
     * @return true，处理中
     */
    default boolean isProcessing() {
        return false;
    }

    /**
     * 添加扩展信息
     *
     * @param key   key值
     * @param value value值
     */
    default void addExtInfo(String key, Object value) {
        if (key == null || value == null) {
            return;
        }

        JSONObject extInfo = getExtInfo();
        if (extInfo == null) {
            extInfo = new JSONObject();
            setExtInfo(extInfo);
        }
        extInfo.put(key, value);
    }

    /** 从拓展信息中删除指定信息 */
    default void removeExtInfo(String key) {
        if (key == null) {
            return;
        }
        getExtInfo().remove(key);
    }

    /**
     * 从扩展信息中获取字符串
     * @param key key值
     * @return value 值
     */
    default String getExtInfo(String key) {
        return getStringFromExtInfo(key);
    }

    /**
     * 从扩展信息中获取字符串
     *
     * @param key key值
     * @return value值
     */
    default String getStringFromExtInfo(String key) {
        return Optional.ofNullable(getExtInfo())
            .map(json -> json.getString(key))
            .orElse(null);
    }

    /**
     * 从扩展信息中获取字符串，提供默认值
     *
     * @param key          key值
     * @param defaultValue 默认值
     * @return value值，如果不存在则返回默认值
     */
    default String getStringFromExtInfo(String key, String defaultValue) {
        return Optional.ofNullable(getStringFromExtInfo(key))
            .orElse(defaultValue);
    }

    /**
     * 从扩展信息中获取整数
     *
     * @param key key值
     * @return value值
     */
    default Integer getIntegerFromExtInfo(String key) {
        return Optional.ofNullable(getExtInfo())
            .map(json -> json.getInteger(key))
            .orElse(null);
    }

    /**
     * 从扩展信息中获取整数，提供默认值
     *
     * @param key          key值
     * @param defaultValue 默认值
     * @return value值，如果不存在则返回默认值
     */
    default Integer getIntegerFromExtInfo(String key, Integer defaultValue) {
        return Optional.ofNullable(getIntegerFromExtInfo(key))
            .orElse(defaultValue);
    }

    /**
     * 从扩展信息中获取布尔类型
     *
     * @param key key值
     * @return value值
     */
    default Boolean getBooleanFromExtInfo(String key) {
        return Optional.ofNullable(getExtInfo())
            .map(json -> json.getBoolean(key))
            .orElse(null);
    }

    /**
     * 从扩展信息中获取布尔类型，提供默认值
     *
     * @param key          key值
     * @param defaultValue 默认值
     * @return value值，如果不存在则返回默认值
     */
    default Boolean getBooleanFromExtInfo(String key, Boolean defaultValue) {
        return Optional.ofNullable(getBooleanFromExtInfo(key))
            .orElse(defaultValue);
    }

    /**
     * 从扩展信息中获取信息
     *
     * @param key 关键字
     * @param clz 类型
     * @param <T> 返回类型
     * @return 返回
     */
    default <T> T getExtValue(String key, Class<T> clz) {
        JSONObject extInfo = getExtInfo();
        if (extInfo == null || !extInfo.containsKey(key)) {
            return null;
        }
        return extInfo.getObject(key, clz);
    }

    /**
     * 从扩展信息中获取信息
     *
     * @param key 关键字
     * @param clz 类型
     * @param <T> 返回类型
     * @return 返回
     */
    default <T> T getExtInfo(String key, Class<T> clz) {
        return getExtValue(key, clz);
    }

    /**
     * 清空扩展信息
     */
    default void clearExtInfo() {
        setExtInfo(null);
    }
}
