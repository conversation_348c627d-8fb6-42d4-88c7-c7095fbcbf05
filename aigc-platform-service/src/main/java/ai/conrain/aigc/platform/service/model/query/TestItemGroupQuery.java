package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;
import java.util.List;

import java.io.Serializable;

/**
 * TestItemGroupQuery
 *
 * @version TestItemGroupService.java v 0.1 2024-12-19 01:24:06
 */
@Data
public class TestItemGroupQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 测试计划id */
    private Integer planId;

    /** 测试项目id */
    private Integer itemId;

    /** 类型，TRAIN、CREATIVE */
    private String type;

    /** 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED */
    private String status;

    /** 轮数 */
    private Integer roundsNum;

    /** 正向数量 */
    private Integer positiveNum;

    /** 负向数量 */
    private Integer negativeNum;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 对照参数信息 */
    private String comparisonParams;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    private List<Integer> itemIds;

}
