package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.alibaba.fastjson.JSONObject;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * StatsSaleIndicatorsVO
 *
 * @version StatsSaleIndicatorsService.java v 0.1 2025-05-08 04:38:31
 */
@Data
public class StatsSaleIndicatorsVO implements Serializable, IExtModel {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    private String statsDate;

    /** 用户 id（渠道/销售/运营） */
    private Integer userId;

    /** 名称（渠道/销售/运营） */
    private String name;

    /** 父级 id，默认为 0 */
    private Integer parentId = 0;

    /** 服装体验量 */
    private Integer clothesExpCount;

    /** 客户转换量（新签 3999 以上） */
    private Integer customerConversionCount;

    /** 客户消耗点数 */
    private Integer customerConsumptionPoints;

    /** 活跃客户率 */
    private String customerActivityRate;

    /** 客户复购率 */
    private String customerRepurchaseRate;

    /** 定制模特数量 */
    private String customModelCustomers;

    /** 定制场景数量 */
    private String customSceneCustomers;

    /** 大于 60 天未充值的客户 */
    private Integer customerProtectionMetrics;

    /** 销售出图数量 */
    private Integer createCount;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /** 扩展字段 */
    private JSONObject extInfo;

    /** 子数据 */
    private List<StatsSaleIndicatorsVO> children;
}
