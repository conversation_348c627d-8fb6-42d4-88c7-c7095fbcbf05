package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.PointLogTypeEnum;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * UserPointLogVO
 *
 * @version UserPointLogService.java v 0.1 2024-06-21 03:18:16
 */
@Data
public class UserPointLogVO implements IExtModel, Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 类型，充值单、服装建模、套餐外创、创作退回作等 */
    private PointLogTypeEnum type;

    /** 关联id */
    private Integer relatedId;

    /** 算力点 */
    private Integer point;

    /** 赠送点数 */
    private Integer givePoint;

    /** 体验算力点 */
    private Integer experiencePoint;

    /** 模型算力点 */
    private Integer modelPoint;

    /** 操作id */
    private Integer operatorId;

    /** 备注 */
    private String memo;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /** 扩展信息 */
    private JSONObject extInfo;

    /** 原始算力点 */
    @JsonIgnore
    private Integer originPoint;

    /** 目标算力点 */
    @JsonIgnore
    private Integer targetPoint;

    /** 原始赠送积分 */
    @JsonIgnore
    private Integer originGivePoint;

    /** 目标赠送积分 */
    @JsonIgnore
    private Integer targetGivePoint;

    /** 原始体验算力点 */
    @JsonIgnore
    private Integer originExperiencePoint;

    /** 目标体验算力点 */
    @JsonIgnore
    private Integer targetExperiencePoint;

    /** 原始服装套餐内点数 */
    @JsonIgnore
    private Integer originModelPoint;

    /** 目标服装套餐内点数 */
    @JsonIgnore
    private Integer targetModelPoint;

    /** 关联的模型名 */
    @JsonIgnore
    private String modelName;
}
