package ai.conrain.aigc.platform.service.model.common;

import lombok.Getter;

@Getter
public enum ResultCode {

    SUCCESS("SUCCESS", "成功"),

    SYS_ERROR("SYS_ERROR", "系统异常"),

    PARAM_INVALID("PARAM_INVALID", "请求参数无效"),

    BIZ_FAIL("BIZ_FAIL", "业务处理失败"),

    LOGIN_EXPIRED("LOGIN_EXPIRED", "登录态已过期，需要重新登录"),

    RESET_PSWD_EXPIRED("RESET_PSWD_EXPIRED", "重置密码已状态已过期，请重新发起流程"),

    USER_NOT_EXISTS("USER_NOT_EXISTS", "登录名错误"),

    MOBILE_NOT_EXISTS("MOBILE_NOT_EXISTS", "手机号码未注册"),

    MOBILE_ALREADY_EXISTS("MOBILE_ALREADY_EXISTS", "手机号码已被注册"),

    CORP_NAME_ALREADY_EXISTS("CORP_NAME_ALREADY_EXISTS", "该企业已注册，请联系贵司管理员在团队管理中进行账号添加！"),

    PASSWORD_ERROR("PASSWORD_ERROR", "密码错误"),

    OVER_MAX_FAIL_CNT("OVER_MAX_FAIL_CNT", "超出密码最大次数"),

    ILLEGAL_PERMISSION("ILLEGAL_PERMISSION", "权限不正确"),

    ILLEGAL_CAPTCHA("ILLEGAL_CAPTCHA", "验证码错误"),

    ILLEGAL_REPEAT_PSWD("ILLEGAL_REPEAT_PSWD", "密码不一致"),

    ILLEGAL_PASSWORD_TYPE("ILLEGAL_PASSWORD_TYPE", "密码过于简单"),

    ILLEGAL_ORDER_STATUS("ILLEGAL_ORDER_STATUS", "状态不正确"),

    CANNOT_ACQUIRE_LOCK("CANNOT_ACQUIRE_LOCK", "当前业务正在处理中，请稍候再试"),

    DUPLICATE_REQ("DUPLICATE_REQ", "不要重复提交请求"),

    SETTLE_ALREADY_FINISHED("SETTLE_ALREADY_FINISHED", "当前商户已完成结算"),

    DUPLICATE_TRANSACTION_NO("DUPLICATE_TRANSACTION_NO", "重复的交易流水"),

    OVER_MAX_SUB_COUNT("OVER_MAX_SUB_COUNT", "超出最大子账号数量"),

    USER_IS_DISABLED("USER_IS_DISABLED", "用户已停用"),
    USER_IS_REVIEW("USER_IS_REVIEW", "用户正在审核中"),
    USER_IS_REJECT("USER_IS_REJECT", "用户审核被拒绝"),

    CUSTOMER_ALREADY_EXISTS("CUSTOMER_ALREADY_EXISTS", "昵称已被注册"),

    PAY_CACHE_EXPIRED("PAY_CACHE_EXPIRED", "页面太久没刷新了，请刷新页面后重试"),

    NOT_IN_WHILTE_LIST("NOT_IN_WHILTE_LIST", "手机号未注册，请先注册！"),

    IMAGE_POINT_OVER_LIMIT("IMAGE_POINT_OVER_LIMIT", "图片算力点已超限"),

    ACTIVE_PROMPT_HAS_EXIST("ACTIVE_PROMPT_HAS_EXIST", "已存在未完成的prompt任务"),

    SYSTEM_LORA_SHOW_IMAGE_NONE("SYSTEM_LORA_SHOW_IMAGE_NONE", "系统lora图片未上传"),

    NOT_QUEUE_CANNOT_CANCEL("NOT_QUEUE_CANNOT_CANCEL", "非队列状态，无法取消"),

    MUSE_POINT_OVER_LIMIT("MUSE_POINT_OVER_LIMIT", "缪斯点已超限"),

    MATERIAL_MODEL_NOT_ENABLED("MATERIAL_MODEL_NOT_ENABLED", "素材模型状态不可用"),

    CREATE_VIDEO_NEED_VIP("CREATE_VIDEO_NEED_VIP", "该功能仅为付费客户开放，请先充值"),

    CANNOT_ACQUIRE_LOCK_MODEL("CANNOT_ACQUIRE_LOCK_MODEL", "当前模型配置已非最新，请保存数据并刷新后重试"),

    TEXT_MODERATION("TEXT_MODERATION", "文本审核不通过"),

    FACE_ILLEGAL_CONFIG("FACE_ILLEGAL_CONFIG", "当前模特配置异常，请检查配置"),

    ILLEGAL_USAGE_INFO("ILLEGAL_USAGE_INFO", "当前请求未填写用途信息，请刷新页面后重试"),

    DUPLICATE_CLOTH_CONFIGURATION("DUPLICATE_CLOTH_CONFIGURATION", "重复配置服装"),

    MATERIAL_NOT_FOUND("MATERIAL_NOT_FOUND", "未找到素材"),

    MATERIAL_NOT_TAGGED("MATERIAL_NOT_TAGGED", "素材没有完成打标"),

    IMAGE_PATH_ALREADY_EXISTS("IMAGE_PATH_ALREADY_EXISTS", "图片路径已存在"),

    MODEL_ALREADY_REFUNDED("MODEL_ALREADY_REFUNDED", "该模型已完成退点，无法重复操作"),

    MODEL_ALREADY_DEDUCTED("MODEL_ALREADY_DEDUCTED", "该模型已完成扣点，无法重复操作"),

    POINT_CALCULATION_ERROR("POINT_CALCULATION_ERROR", "业务点数计算异常"),

    ;

    private String code;
    private String desc;

    ResultCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
