package ai.conrain.aigc.platform.service.model.query;

import java.io.Serializable;
import java.util.Date;


import lombok.Data;

/**
 * SystemConfigQuery
 *
 * @version SystemConfigService.java v 0.1 2024-01-20 01:21:37
 */
@Data
public class SystemConfigQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 配置key */
    private String confKey;

    /** 配置值，状态非0时直接取该值 */
    private String confValue;

    /** 执行变更后的配置值 */
    private String confValueNext;

    /** 状态，0失效、1正常、2待变更 */
    private String status;

    /** 变更生效时间 */
    private Date effectTime;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
