package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.CommonTaskEnums;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TASK_ORDER;

/**
 * CommonTaskVO
 *
 * @version CommonTaskService.java v 0.1 2024-12-31 09:04:22
 */
@Data
public class CommonTaskVO implements IExtModel,Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 任务id */
	private Integer id;

	/** 归属主账号id */
	private Integer userId;

	/** 操作员/子账号id */
	private Integer operatorId;

	/** 任务类型, video_generation/extend_video/lip_sync等 */
	private String taskType;

	/** 任务状态, INIT/PENDING/RUNNING/COMPLETED/FAILED/CANCELED */
	private String taskStatus;

	/** 第三方平台名称 */
	private String outTaskPlatform;

	/** 第三方平台任务id */
	private String outTaskId;

	/** 任务状态 */
	private String outTaskStatus;

	/** 自定义请求参数 */
	private String reqBizParams;

	/** 关联业务类型 */
	private String relatedBizType;

	/** 关联业务id */
	private String relatedBizId;

	/** 扩展 */
	private JSONObject extInfo;

	/** 任务开始时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date taskStartTime;

	/** 任务结束时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date taskEndTime;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

	/** 完整请求报文，主要用于请求外部http或comfyui服务的重试 */
	private String completeRequest;

	/** 结果详情 */
	private String retDetail;

	public boolean isTaskCompleted(){
		return CommonTaskEnums.TaskStatus.COMPLETED.name().equalsIgnoreCase(taskStatus);
	}

	/** 获取任务 order */
    public Integer getTaskOrder() {
		Integer order = this.getExtValue(KEY_TASK_ORDER, Integer.class);
		return order == null ? 0 : order;
    }

	/** 添加结果详情 */
	public void addRetDetail(String key, Object value) {
		JSONObject retDetailJson = JSONObject.parseObject(this.retDetail);
		if (retDetailJson == null) {
			retDetailJson = new JSONObject();
		}
		retDetailJson.put(key, value);
		this.retDetail = retDetailJson.toJSONString();
	}

	/** 获取结果详情 */
	public <T> T getRetDetail(String key, Class<T> clazz) {
		JSONObject retDetailJson = JSONObject.parseObject(this.retDetail);
		if (retDetailJson == null) {
			return null;
		}
		return retDetailJson.getObject(key, clazz);
	}
}
