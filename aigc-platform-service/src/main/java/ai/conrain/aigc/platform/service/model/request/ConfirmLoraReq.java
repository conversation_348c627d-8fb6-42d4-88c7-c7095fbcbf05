package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotNull;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import java.util.List;
import lombok.Data;

@Data
public class ConfirmLoraReq {

    @NotNull
    private Integer id;

    private List<Integer> testFaces;

    private List<Integer> testScenes;

    private Integer testNum;

    // 测试图片比例
    private List<String> testImgProportions;

    //每组图片数，默认20
    private Integer imgNumPerGroup;

    // 测试图片模式，random：随机 | cartesian_product：笛卡尔积
    // @see CommonConstants#TEST_IMG_MODE_RANDOM
    private String testImgMode = "random";

    // 训练次数
    private Integer maxTrainStep = 2500;

    // lora类型, flux|sdxl
    private String loraType = CommonConstants.FLUX;

    /** 测试图搭配 */
    private ClothCollocationModel testClothCollocation;

    private String lr;
    private String contentOrStyle;
    private String rank;
    private String alpha;
    private String dropout;
    private String resolution = "1024";

    /** 训练扩展信息 */
    private JSONObject trainExtInfo;

    /** 是否批量确认 */
    private boolean batchConfirm;
}