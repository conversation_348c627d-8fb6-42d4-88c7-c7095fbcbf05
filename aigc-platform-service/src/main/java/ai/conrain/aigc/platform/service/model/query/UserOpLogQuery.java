package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * UserOpLogQuery
 *
 * @version UserOpLogService.java v 0.1 2024-01-25 09:31:00
 */
@Data
public class UserOpLogQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 主账号用户id */
    private Integer masterUserId;

    /** 操作人用户id */
    private Integer operatorUserId;

    /** 角色类型 */
    private String roleType;

    /** 操作行为，新增、修改、废弃 */
    private String opType;

    /** 商品、订单、赊账、收入、支出、还款、客户、子账号 */
    private String opBizType;

    /** 操作目标id */
    private String opBizNo;

    /** 备注 */
    private String memo;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 变更前明细 */
    private String detailBefore;

    /** 变更后明细 */
    private String detailAfter;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
