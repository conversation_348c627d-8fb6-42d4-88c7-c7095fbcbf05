package ai.conrain.aigc.platform.service.model.vo;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * UserOrganizationVO
 *
 * @version UserOrganizationService.java v 0.1 2024-07-12 03:53:58
 */
@Data
public class UserOrganizationVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	private Integer id;

	/** 用户（主账号或操作员）id */
	private Integer userId;

	/** 组织id */
	private Integer orgId;

	/** 创建人操作员账号id */
	private Integer creatorOperatorUserId;

	/** 修改人操作员账号id */
	private Integer modifierOperatorUserId;

	/** 扩展信息 */
	private String extInfo;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

}
