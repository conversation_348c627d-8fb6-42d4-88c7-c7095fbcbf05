package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * OrderSettlementQuery
 *
 * @version OrderSettlementService.java v 0.1 2025-05-22 03:41:06
 */
@Data
public class OrderSettlementQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 订单号 */
    private String orderNo;

    /** 订单结算类型 */
    private String type;

    /** 渠道商实体id */
    private Integer distributorCorpId;

    /** 渠道商实体名称 */
    private String distributorCorpName;

    /** 结算主体类型 */
    private String principalType;

    /** 结算主体 id */
    private Integer principalId;

    /** 状态，0初始化、1订单关闭、2待结算、3结算中、4结算成功 */
    private Integer status;

    /** 排除状态 */
    private List<Integer> statusNotIn;

    /** 结算id */
    private String settleId;

    /** 结算完成时间 */
    private Date settleTime;

    /** 总金额 */
    private BigDecimal totalAmount;

    /** 渠道费率 */
    private BigDecimal channelRate;

    /** 结算金额 */
    private BigDecimal settleAmount;

    /** 扩展信息 */
    private String extInfo;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
