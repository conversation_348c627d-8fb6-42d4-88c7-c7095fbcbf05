package ai.conrain.aigc.platform.service.model.query;

import ai.conrain.aigc.platform.service.validation.SimpleDate;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * UserFavorQuery
 *
 * @version UserFavorService.java v 0.1 2025-03-08 11:26:59
 */
@Data
public class UserFavorQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 收藏对象的id, model_id, element_id, taskId */
    private Integer itemId;

    /** 类型 */
    private String type;

    /**  */
    private Integer modelId;

    /** 备注 */
    private String memo;

    /** 操作人id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 扩展字段 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    //起始日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateFrom;

    //截止日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateTo;
}
