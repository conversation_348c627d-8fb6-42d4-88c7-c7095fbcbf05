package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.alibaba.fastjson.JSONObject;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * StatsOperationIndicatorsVO
 *
 * @version StatsOperationIndicatorsService.java v 0.1 2025-05-16 11:38:50
 */
@Data
public class StatsOperationIndicatorsVO implements Serializable, IExtModel {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	private Integer id;

	/** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
	private String statsType;

	/** 统计日期: 格式为yyyy-MM-dd */
	private String statsDate;

	/** 用户 id（渠道/销售/运营） */
	private Integer userId;

	/** 名称（渠道/销售/运营） */
	private String name;

	/** 客户转换量（新签 3999 以上） */
	private Integer customerConversionCount = 0;

	/** 客户消耗点数 */
	private Integer customerConsumptionPoints = 0;

	/** 平均消耗点数 */
	private String customerConsumptionPointsAvg = "0";

	/** 活跃客户率 */
	private String customerActivityRate  = "0";

	/** 客户复购率 */
	private String customerRepurchaseRate = "0";

	/** 定制模特比例 */
	private String customModelCustomers = "0";

	/** 定制场景比例 */
	private String customSceneCustomers = "0";

	/** 大于 60 天未充值的客户 */
	private Integer customerProtectionMetrics = 0;

	/** 交付服装量 */
	private Integer deliveryClothingCount = 0;

	/** 审核服装量 */
	private Integer approveClothingCount = 0;

	/** 审核错误率 */
	private String approveErrorRate = "0";

	/** 服装返点率 */
	private String garmentRebateRate  = "0";

	/** 客户投诉率 */
	private String customerComplaintRate = "0";

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 更新时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

	/** 扩展字段 */
	private JSONObject extInfo;

	/** 客户总数 */
	private Integer customerTotalCount = 0;

	/** 视频数量 */
	private Integer videoCount = 0;

	/** 客均视频数量 */
	private String videoCountAvg = "0.00";

	/** 上传服装总数 */
	private Integer customerUploadMaterialCount = 0;
}
