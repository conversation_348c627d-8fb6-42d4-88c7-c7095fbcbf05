package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * ImageCaptionUserQuery
 *
 * @version ImageCaptionUserService.java v 0.1 2025-07-30 08:19:29
 */
@Data
public class ImageCaptionUserQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    private String type;

    /** 主键 */
    private Integer id;

    /** 图片ID */
    private Integer imageId;

    /** 用户ID */
    private Integer userId;

    /** 对原始的image_caption_user记录进行修改 */
    private Integer originalId;

    /** 标注 */
    private String caption;

    /** 标注版本 */
    private String captionVersion;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;


    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
