package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * PromptDictQuery
 *
 * @version PromptDictService.java v 0.1 2024-11-15 07:24:51
 */
@Data
public class PromptDictQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 名词 */
    private String word;

    /** 对应的prompt */
    private String prompt;

    /** 展示图url */
    private String showImage;

    /** 标签列表，多个以逗号隔开 */
    private String tags;

    /** 备注 */
    private String memo;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
