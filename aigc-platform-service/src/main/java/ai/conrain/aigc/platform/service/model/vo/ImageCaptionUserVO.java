package ai.conrain.aigc.platform.service.model.vo;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * ImageCaptionUserVO
 *
 * @version ImageCaptionUserService.java v 0.1 2025-07-30 08:19:29
 */
@Data
public class ImageCaptionUserVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 主键 */
	private Integer id;

    private String type;

	/** 图片ID */
	private Integer imageId;

	/** 用户ID */
	private Integer userId;

	/** 对原始的image_caption_user记录进行修改 */
	private Integer originalId;

	/** 标注 */
	private JSONObject caption;

	/** 标注版本 */
	private String captionVersion;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

}
