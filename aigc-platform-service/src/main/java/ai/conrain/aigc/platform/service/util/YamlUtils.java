/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.LoaderOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import java.util.Map;

/**
 * yaml格式解析工具
 *
 * <AUTHOR>
 * @version : YamlUtils.java, v 0.1 2024/2/4 11:57 renxiao.wu Exp $
 */
@Slf4j
public class YamlUtils {
    /**
     * 将yaml字符串解析为对象
     *
     * @param yamlStr 字符串
     * @param clazz   目标对象类型
     * @param <T>     对象类型
     * @return 返回结果
     */
    @SuppressWarnings("VulnerableCodeUsages")
    public static <T> T parseToBean(String yamlStr, Class<T> clazz) {
        LoaderOptions options = new LoaderOptions();
        Yaml yaml = new Yaml(new Constructor(clazz, options));
        return yaml.load(yamlStr);
    }

    /**
     * 将yaml字符串解析为json
     *
     * @param yamlStr 字符串
     * @return json结果
     */
    public static JSONObject parseToJson(String yamlStr) {
        Yaml yaml = new Yaml();
        Map<String, Object> map = yaml.load(yamlStr);
        return new JSONObject(map);
    }

    /**
     * 将对象格式化为yaml字符串
     *
     * @param obj 对象
     * @return yaml字符串
     */
    public static String formatBean(Object obj) {
        Yaml yaml = new Yaml();
        return yaml.dump(obj);
    }

    /**
     * 将对象格式化为yaml字符串
     *
     * @param json 对象
     * @return yaml字符串
     */
    public static String formatJson(JSONObject json) {
        return formatMap(json.getInnerMap());
    }

    /**
     * 将对象格式化为yaml字符串
     *
     * @param map map对象
     * @return yaml字符串
     */
    public static String formatMap(Map<String, Object> map) {
        // 配置YAML输出格式
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setPrettyFlow(true);

        // 创建Yaml实例并转换Map为YAML格式的字符串
        Yaml yaml = new Yaml(options);
        return yaml.dump(map);
    }
}
