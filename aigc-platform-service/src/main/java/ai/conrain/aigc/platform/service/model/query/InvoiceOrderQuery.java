package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * InvoiceOrderQuery
 *
 * @version InvoiceOrderService.java v 0.1 2024-06-27 12:49:39
 */
@Data
public class InvoiceOrderQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 关联的用户id */
    private Integer userId;

    /** 关联的操作员用户id */
    private Integer operatorId;

    /** 关联的开票id */
    private Integer invoiceId;

    /** 关联的订单id */
    private Integer orderId;

    /** 备注 */
    private String memo;

    /** 扩展信息 */
    private String extInfo;


    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
