package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ComfyuiTplInfo;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_QUEUE_SIZE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCHEDULE;

/**
 * CreativeTaskVO
 *
 * @version CreativeTaskService.java v 0.1 2024-05-22 12:04:27
 */
@Data
public class CreativeTaskVO implements IExtModel, Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** 任务id */
    private Integer id;

    /** 批次id */
    private Integer batchId;

    /** 归属主账号id */
    private Integer userId;

    /** 模型id */
    private Integer modelId;

    private CreativeTypeEnum type;

    /** 图片比例，3:4、1:1等 */
    private String imageProportion;

    /** 批次数量 */
    private Integer batchCnt;

    /** ComfyUI返回的唯一标识 */
    private String promptId;

    /** 结果图片路径 */
    private String resultPath;

    /** 状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED */
    private CreativeStatusEnum status;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /** aigc请求参数 */
    @Deprecated
    private String aigcRequest;

    /** 结果图片url列表 */
    private List<String> resultImages;

    /** 扩展信息 */
    private JSONObject extInfo;

    //comfyui tpl info
    private ComfyuiTplInfo tplInfo;

    /**  前置任务ID */
    private Integer preTaskId;

    // 新增：模特名和场景名列表
    private List<String> faceNameList;
    private List<String> sceneNameList;

    @Override
    public boolean isProcessing() {
        return status == CreativeStatusEnum.PROCESSING;
    }

    /**
     * 添加结果图片
     *
     * @param imageUrl 图片url
     */
    public void addResultImage(String imageUrl) {
        if (resultImages == null) {
            resultImages = new ArrayList<>();
        }

        resultImages.add(imageUrl);
        switch (type) {
            // 排序, 把 _0_ 排在 _1_ 前面  例: https://***.com/202508/100156/product_3669419_1754620548_578216_0_cyapv.jpg?Expires=***
            case REPAIR_DETAIL:
                String regex = "(\\d+)_(\\d)_([A-Za-z]+)";
                Pattern pattern = Pattern.compile(regex);
                resultImages.sort((o1, o2) -> {
                    Matcher matcher1 = pattern.matcher(o1);
                    Matcher matcher2 = pattern.matcher(o2);
                    if (matcher1.find() && matcher2.find()) {
                        int index1 = Integer.parseInt(matcher1.group(2));
                        int index2 = Integer.parseInt(matcher2.group(2));
                        return index1 - index2;
                    }
                    return 0;
                });
                break;
            default:
                break;
        }
    }

    public void clearResultImage() {
        if (resultImages != null) {
            resultImages.clear();
        }
    }

    public void clearExtInfo() {
        if (extInfo != null) {
            extInfo.remove(KEY_SCHEDULE);
            extInfo.remove(KEY_QUEUE_SIZE);
        }
    }
}
