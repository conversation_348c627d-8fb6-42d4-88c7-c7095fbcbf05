package ai.conrain.aigc.platform.service.model.vo;

import java.io.Serializable;
import java.util.Date;

import ai.conrain.aigc.platform.service.enums.ConfigStatusEnum;
import ai.conrain.aigc.platform.service.model.common.ModifyTimeClz;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * SystemConfigVO
 *
 * @version SystemConfigService.java v 0.1 2024-01-20 01:21:37
 */
@Data
public class SystemConfigVO implements ModifyTimeClz, Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 配置key */
    private String confKey;

    /** 配置值，状态非0时直接取该值 */
    private String confValue;

    /** 执行变更后的配置值 */
    private String confValueNext;

    /** 状态 */
    private ConfigStatusEnum status;

    /** 变更生效时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectTime;

    private String memo;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

}
