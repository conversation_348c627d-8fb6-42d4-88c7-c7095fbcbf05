/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.vo;

import java.io.Serializable;
import java.util.List;


import lombok.Data;

/**
 * 进行中的和今日的创作批次列表
 *
 * <AUTHOR>
 * @version : CreativeActiveAndToday.java, v 0.1 2024/5/10 22:08 renxiao.wu Exp $
 */
@Data
public class CreativeActiveAndToday implements Serializable {
    private static final long serialVersionUID = 1231836111690684300L;
    /** 进行中的创作批次 */
    @Deprecated
    private CreativeBatchVO active;
    /** 今日的创作批次 */
    private List<CreativeBatchVO> todayList;
}
