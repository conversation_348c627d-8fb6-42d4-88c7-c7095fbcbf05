package ai.conrain.aigc.platform.service.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.util.Date;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSONObject;

/**
 * ImageCaseQuery
 *
 * @version ImageCaseService.java v 0.1 2024-12-10 03:56:24
 */
@Data
public class ImageCaseQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 图片url */
    private String url;

    /** 缩略图片url */
    private String miniUrl;

    /** 任务id */
    private Integer taskId;

    /** 批次id */
    private Integer batchId;

    /** 存储地址 */
    private String storePath;

    /** 存储服务器 */
    private String storeServer;

    /** 状态，ENABLED、DISABLED */
    private String status;

    /** 备注 */
    private String memo;

    /** 用户id */
    private Integer userId;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 同步状态*/
    private Boolean syncStatus;

    /** 同步时间 */
    private Date syncTime;

    /** 同步次数 */
    private Integer reSyncCount;

    /** 最大同步次数 */
    private Integer maxSyncCount;

    private List<Integer> tagIds;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /** 开始查询时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginSearchTime;

    /** 结束查询时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSearchTime;

    /** 图片案例类型 */
    private String imageCaseType;

    /** 主键 Id 列表 */
    private List<Integer> caseIdList;

    /** 扩展信息 */
    private JSONObject extInfo;
}
