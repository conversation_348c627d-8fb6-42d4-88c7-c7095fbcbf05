package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * CreativeTaskQuery
 *
 * @version CreativeTaskService.java v 0.1 2024-05-22 12:04:27
 */
@Data
public class CreativeTaskQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 任务id */
    private Integer id;

    private List<Integer> ids;

    /** 批次id */
    private Integer batchId;

    /** 归属主账号id */
    private Integer userId;

    /** 模型id */
    private Integer modelId;

    private String type;

    /** 图片比例，3:4、1:1等 */
    private String imageProportion;

    /** 批次数量 */
    private Integer batchCnt;

    /** ComfyUI返回的唯一标识 */
    private String promptId;

    /** 结果图片路径 */
    private String resultPath;

    /** 状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED */
    private String status;

    /** 操作者id */
    private Integer operatorId;


    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** aigc请求参数 */
    private String aigcRequest;

    /** 结果图片url列表 */
    private String resultImages;

    /** 扩展信息 */
    private String extInfo;

    private List<Integer> elementIds;

    private Integer elementId;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
